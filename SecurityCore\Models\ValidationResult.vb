Public Class ValidationResult
    Public Property IsValid As Boolean
    Public Property ErrorMessage As String
    Public Property ValidationTime As DateTime
    Public Property AdditionalData As Dictionary(Of String, Object)
    
    Public Sub New()
        ValidationTime = DateTime.UtcNow
        AdditionalData = New Dictionary(Of String, Object)
    End Sub
    
    Public Sub New(isValid As Boolean, errorMessage As String)
        Me.New()
        Me.IsValid = isValid
        Me.ErrorMessage = errorMessage
    End Sub
    
    Public Sub New(isValid As Boolean, errorMessage As String, additionalData As Dictionary(Of String, Object))
        Me.New(isValid, errorMessage)
        If additionalData IsNot Nothing Then
            Me.AdditionalData = additionalData
        End If
    End Sub
    
    Public Overrides Function ToString() As String
        Return $"Valid: {IsValid}, Message: {ErrorMessage}, Time: {ValidationTime}"
    End Function
End Class
