Imports System.Net.Http
Imports System.Text
Imports System.Threading.Tasks
' Imports Newtonsoft.Json - مؤقت

Public Class SecureTelegramNotifier

    ' بيانات مشفرة للبوت (يجب تشفيرها في الإنتاج)
    Private Shared ReadOnly _encryptedBotToken As String = "ENCRYPTED_BOT_TOKEN_HERE"
    Private Shared ReadOnly _encryptedAdminChatId As String = "ENCRYPTED_ADMIN_CHAT_ID_HERE"
    Private Shared ReadOnly _encryptedChannelId As String = "ENCRYPTED_CHANNEL_ID_HERE"

    Private Shared _httpClient As HttpClient

    Shared Sub New()
        _httpClient = New HttpClient()
        _httpClient.Timeout = TimeSpan.FromSeconds(30)
    End Sub

    Public Shared Async Function SendToAdmin(message As String) As Task
        Try
            Dim botToken As String = DecryptBotToken()
            Dim chatId As String = DecryptAdminChatId()

            Await SendMessage(botToken, chatId, message)

        Catch ex As Exception
            LogError($"Failed to send admin message: {ex.Message}")
        End Try
    End Function

    Public Shared Async Function SendToChannel(message As String) As Task
        Try
            Dim botToken As String = DecryptBotToken()
            Dim channelId As String = DecryptChannelId()

            Await SendMessage(botToken, channelId, message)

        Catch ex As Exception
            LogError($"Failed to send channel message: {ex.Message}")
        End Try
    End Function

    Public Shared Async Function SendEmergencyAlert(message As String) As Task
        Try
            ' إرسال للإدمن والقناة في نفس الوقت
            Dim tasks As New List(Of Task) From {
                SendToAdmin($"🚨 EMERGENCY ALERT 🚨{Environment.NewLine}{message}"),
                SendToChannel($"🚨 EMERGENCY ALERT 🚨{Environment.NewLine}{message}")
            }

            Await Task.WhenAll(tasks)

        Catch ex As Exception
            LogError($"Failed to send emergency alert: {ex.Message}")
        End Try
    End Function

    Private Shared Async Function SendMessage(botToken As String, chatId As String, message As String) As Task
        Try
            Dim url As String = $"https://api.telegram.org/bot{botToken}/sendMessage"

            Dim payload As New With {
                .chat_id = chatId,
                .text = message,
                .parse_mode = "HTML",
                .disable_web_page_preview = True
            }

            ' مؤقت - يجب استبداله بـ JsonConvert لاحقاً
            Dim jsonPayload As String = "{""chat_id"":""" & chatId & """,""text"":""" & message.Replace("""", "\""") & """,""parse_mode"":""HTML""}"
            Dim content As New StringContent(jsonPayload, Encoding.UTF8, "application/json")

            Dim response As HttpResponseMessage = Await _httpClient.PostAsync(url, content)

            If Not response.IsSuccessStatusCode Then
                Dim errorContent As String = Await response.Content.ReadAsStringAsync()
                LogError($"Telegram API error: {response.StatusCode} - {errorContent}")
            End If

        Catch ex As Exception
            LogError($"Failed to send Telegram message: {ex.Message}")
        End Try
    End Function

    Private Shared Function DecryptBotToken() As String
        Try
            ' في الإنتاج، يجب فك تشفير التوكن الحقيقي
            ' هذا مثال للتوضيح فقط
            If _encryptedBotToken = "ENCRYPTED_BOT_TOKEN_HERE" Then
                ' إرجاع توكن تجريبي أو رمي استثناء
                Throw New InvalidOperationException("Bot token not configured")
            End If

            Return AdvancedEncryption.DecryptWithHardwareKey(_encryptedBotToken)
        Catch ex As Exception
            LogError($"Failed to decrypt bot token: {ex.Message}")
            Throw New SecurityException("Bot token decryption failed")
        End Try
    End Function

    Private Shared Function DecryptAdminChatId() As String
        Try
            If _encryptedAdminChatId = "ENCRYPTED_ADMIN_CHAT_ID_HERE" Then
                Throw New InvalidOperationException("Admin chat ID not configured")
            End If

            Return AdvancedEncryption.DecryptWithHardwareKey(_encryptedAdminChatId)
        Catch ex As Exception
            LogError($"Failed to decrypt admin chat ID: {ex.Message}")
            Throw New SecurityException("Admin chat ID decryption failed")
        End Try
    End Function

    Private Shared Function DecryptChannelId() As String
        Try
            If _encryptedChannelId = "ENCRYPTED_CHANNEL_ID_HERE" Then
                Throw New InvalidOperationException("Channel ID not configured")
            End If

            Return AdvancedEncryption.DecryptWithHardwareKey(_encryptedChannelId)
        Catch ex As Exception
            LogError($"Failed to decrypt channel ID: {ex.Message}")
            Throw New SecurityException("Channel ID decryption failed")
        End Try
    End Function

    ' إعداد البيانات المشفرة (يستخدم مرة واحدة لتشفير البيانات)
    Public Shared Sub SetupEncryptedCredentials(botToken As String, adminChatId As String, channelId As String)
        Try
            ' تشفير البيانات وحفظها (هذا للإعداد الأولي فقط)
            Dim encryptedToken As String = AdvancedEncryption.EncryptWithHardwareKey(botToken)
            Dim encryptedAdmin As String = AdvancedEncryption.EncryptWithHardwareKey(adminChatId)
            Dim encryptedChannel As String = AdvancedEncryption.EncryptWithHardwareKey(channelId)

            ' يجب حفظ هذه القيم في الكود أو ملف إعدادات مشفر
            Console.WriteLine($"Encrypted Bot Token: {encryptedToken}")
            Console.WriteLine($"Encrypted Admin Chat ID: {encryptedAdmin}")
            Console.WriteLine($"Encrypted Channel ID: {encryptedChannel}")

        Catch ex As Exception
            LogError($"Failed to setup encrypted credentials: {ex.Message}")
        End Try
    End Sub

    ' إرسال تقرير دوري
    Public Shared Async Function SendPeriodicReport(report As String) As Task
        Try
            Dim message As String = $"📊 تقرير دوري - {DateTime.Now:dd/MM/yyyy HH:mm}

{report}

🕐 التوقيت: {DateTime.Now:yyyy-MM-dd HH:mm:ss} UTC"

            Await SendToAdmin(message)

        Catch ex As Exception
            LogError($"Failed to send periodic report: {ex.Message}")
        End Try
    End Function

    ' إرسال تنبيه تسجيل دخول
    Public Shared Async Function SendLoginAlert(userInfo As String, deviceInfo As String, location As String) As Task
        Try
            Dim message As String = $"🔐 تسجيل دخول جديد

👤 المستخدم: {userInfo}
🖥️ الجهاز: {deviceInfo}
📍 الموقع: {location}
🕐 الوقت: {DateTime.Now:dd/MM/yyyy HH:mm:ss}

✅ تم التحقق من الترخيص بنجاح"

            Await SendToAdmin(message)

        Catch ex As Exception
            LogError($"Failed to send login alert: {ex.Message}")
        End Try
    End Function

    ' إرسال تنبيه انتهاك أمني
    Public Shared Async Function SendSecurityViolation(violationType As String, details As String) As Task
        Try
            Dim message As String = $"🚨 انتهاك أمني مكتشف!

⚠️ نوع الانتهاك: {violationType}
📋 التفاصيل: {details}
🕐 الوقت: {DateTime.Now:dd/MM/yyyy HH:mm:ss}
🖥️ الجهاز: {Environment.MachineName}

🔒 تم اتخاذ إجراءات الحماية اللازمة"

            Await SendToAdmin(message)
            Await SendToChannel(message)

        Catch ex As Exception
            LogError($"Failed to send security violation alert: {ex.Message}")
        End Try
    End Function

    Private Shared Sub LogError(message As String)
        Try
            ' تسجيل الأخطاء
            Dim logFile As String = IO.Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "SecurityLogs", "telegram_errors.log")

            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}"
            IO.File.AppendAllText(logFile, logEntry)

        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    ' تنظيف الموارد
    Public Shared Sub Dispose()
        Try
            _httpClient?.Dispose()
        Catch
            ' تجاهل أخطاء التنظيف
        End Try
    End Sub

End Class
