' Imports Newtonsoft.Json - مؤقت

Public Class License
    Public Property LicenseId As String
    Public Property HardwareFingerprint As String
    Public Property CustomerInfo As CustomerInfo
    Public Property IssueDate As DateTime
    Public Property ExpiryDate As DateTime
    Public Property IsActive As Boolean
    Public Property MaxActivations As Integer
    Public Property CurrentActivations As Integer
    Public Property Signature As String
    Public Property LicenseType As String
    Public Property Features As List(Of String)
    Public Property LastValidationDate As DateTime?
    Public Property ValidationCount As Integer
    Public Property Notes As String
    Public Property AdditionalData As Dictionary(Of String, Object)

    Public Sub New()
        LicenseId = Guid.NewGuid().ToString()
        IssueDate = DateTime.UtcNow
        IsActive = True
        MaxActivations = 1
        CurrentActivations = 0
        ValidationCount = 0
        Features = New List(Of String)
        AdditionalData = New Dictionary(Of String, Object)
        LicenseType = "Standard"
    End Sub

    Public Sub New(customerId As String, hardwareFingerprint As String)
        Me.New()
        Me.HardwareFingerprint = hardwareFingerprint
        Me.CustomerInfo = New CustomerInfo() With {.CustomerId = customerId}
    End Sub

    Public Function IsValid() As Boolean
        Try
            ' فحص الحالة الأساسية
            If Not IsActive Then Return False

            ' فحص تاريخ انتهاء الصلاحية
            If ExpiryDate <= DateTime.UtcNow Then Return False

            ' فحص عدد التفعيلات
            If CurrentActivations >= MaxActivations Then Return False

            ' فحص التوقيع
            If String.IsNullOrEmpty(Signature) Then Return False

            Return True
        Catch
            Return False
        End Try
    End Function

    Public Function IsExpired() As Boolean
        Return ExpiryDate <= DateTime.UtcNow
    End Function

    Public Function DaysUntilExpiry() As Integer
        Try
            Dim timeSpan As TimeSpan = ExpiryDate.Subtract(DateTime.UtcNow)
            Return Math.Max(0, CInt(timeSpan.TotalDays))
        Catch
            Return 0
        End Try
    End Function

    Public Function CanActivate() As Boolean
        Return IsActive AndAlso CurrentActivations < MaxActivations AndAlso Not IsExpired()
    End Function

    Public Sub Activate()
        If CanActivate() Then
            CurrentActivations += 1
            LastValidationDate = DateTime.UtcNow
        Else
            Throw New InvalidOperationException("لا يمكن تفعيل هذا الترخيص")
        End If
    End Sub

    Public Sub Deactivate()
        If CurrentActivations > 0 Then
            CurrentActivations -= 1
        End If
    End Sub

    Public Sub UpdateValidation()
        LastValidationDate = DateTime.UtcNow
        ValidationCount += 1
    End Sub

    Public Sub AddFeature(feature As String)
        If Not Features.Contains(feature) Then
            Features.Add(feature)
        End If
    End Sub

    Public Sub RemoveFeature(feature As String)
        Features.Remove(feature)
    End Sub

    Public Function HasFeature(feature As String) As Boolean
        Return Features.Contains(feature)
    End Function

    Public Function ToJson() As String
        ' مؤقت - يجب استبداله بـ Newtonsoft.Json لاحقاً
        Return "{""placeholder"": ""json""}"
    End Function

    Public Shared Function FromJson(json As String) As License
        ' مؤقت - يجب استبداله بـ Newtonsoft.Json لاحقاً
        Return New License()
    End Function

    Public Function ToByteArray() As Byte()
        Dim json As String = ToJson()
        Return System.Text.Encoding.UTF8.GetBytes(json)
    End Function

    Public Shared Function FromByteArray(data As Byte()) As License
        Dim json As String = System.Text.Encoding.UTF8.GetString(data)
        Return FromJson(json)
    End Function

    Public Function Clone() As License
        Dim json As String = ToJson()
        Return FromJson(json)
    End Function

    Public Function GetSummary() As String
        Return $"License ID: {LicenseId}
Customer: {CustomerInfo?.Name ?? "Unknown"}
Type: {LicenseType}
Status: {If(IsActive, "Active", "Inactive")}
Expires: {ExpiryDate:yyyy-MM-dd}
Activations: {CurrentActivations}/{MaxActivations}
Last Validation: {LastValidationDate?.ToString("yyyy-MM-dd HH:mm") ?? "Never"}"
    End Function

    Public Overrides Function ToString() As String
        Return $"{LicenseId} - {CustomerInfo?.Name ?? "Unknown"} ({If(IsActive, "Active", "Inactive")})"
    End Function

    Public Function Validate() As ValidationResult
        Try
            Dim errors As New List(Of String)

            ' فحص المعرف
            If String.IsNullOrEmpty(LicenseId) Then
                errors.Add("معرف الترخيص مطلوب")
            End If

            ' فحص بصمة العتاد
            If String.IsNullOrEmpty(HardwareFingerprint) Then
                errors.Add("بصمة العتاد مطلوبة")
            End If

            ' فحص معلومات العميل
            If CustomerInfo Is Nothing Then
                errors.Add("معلومات العميل مطلوبة")
            End If

            ' فحص التواريخ
            If IssueDate > DateTime.UtcNow Then
                errors.Add("تاريخ الإصدار لا يمكن أن يكون في المستقبل")
            End If

            If ExpiryDate <= IssueDate Then
                errors.Add("تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ الإصدار")
            End If

            ' فحص التفعيلات
            If MaxActivations <= 0 Then
                errors.Add("عدد التفعيلات المسموحة يجب أن يكون أكبر من صفر")
            End If

            If CurrentActivations < 0 Then
                errors.Add("عدد التفعيلات الحالية لا يمكن أن يكون سالباً")
            End If

            If CurrentActivations > MaxActivations Then
                errors.Add("عدد التفعيلات الحالية يتجاوز الحد المسموح")
            End If

            ' فحص التوقيع
            If String.IsNullOrEmpty(Signature) Then
                errors.Add("التوقيع الرقمي مطلوب")
            End If

            If errors.Count = 0 Then
                Return New ValidationResult(True, "الترخيص صحيح")
            Else
                Return New ValidationResult(False, String.Join("; ", errors))
            End If

        Catch ex As Exception
            Return New ValidationResult(False, $"خطأ في التحقق من الترخيص: {ex.Message}")
        End Try
    End Function

    Public Function GetSecurityInfo() As Dictionary(Of String, Object)
        Return New Dictionary(Of String, Object) From {
            {"LicenseId", LicenseId},
            {"HardwareFingerprint", HardwareFingerprint},
            {"IsActive", IsActive},
            {"ExpiryDate", ExpiryDate},
            {"CurrentActivations", CurrentActivations},
            {"MaxActivations", MaxActivations},
            {"LastValidationDate", LastValidationDate},
            {"ValidationCount", ValidationCount}
        }
    End Function

    Public Sub UpdateFromSecurityInfo(securityInfo As Dictionary(Of String, Object))
        Try
            If securityInfo.ContainsKey("IsActive") Then
                IsActive = CBool(securityInfo("IsActive"))
            End If

            If securityInfo.ContainsKey("CurrentActivations") Then
                CurrentActivations = CInt(securityInfo("CurrentActivations"))
            End If

            If securityInfo.ContainsKey("LastValidationDate") Then
                LastValidationDate = CDate(securityInfo("LastValidationDate"))
            End If

            If securityInfo.ContainsKey("ValidationCount") Then
                ValidationCount = CInt(securityInfo("ValidationCount"))
            End If

        Catch ex As Exception
            Throw New ArgumentException($"خطأ في تحديث معلومات الأمان: {ex.Message}")
        End Try
    End Sub

End Class

Public Enum LicenseStatus
    Active = 1
    Inactive = 2
    Expired = 3
    Suspended = 4
    Revoked = 5
End Enum

Public Class LicenseFeature
    Public Property Name As String
    Public Property Description As String
    Public Property IsEnabled As Boolean
    Public Property ExpiryDate As DateTime?

    Public Sub New()
        IsEnabled = True
    End Sub

    Public Sub New(name As String, description As String)
        Me.New()
        Me.Name = name
        Me.Description = description
    End Sub

    Public Function IsValid() As Boolean
        If Not IsEnabled Then Return False
        If ExpiryDate.HasValue AndAlso ExpiryDate.Value <= DateTime.UtcNow Then Return False
        Return True
    End Function
End Class
