Imports System.Security.Cryptography
Imports System.Text
Imports System.Management

Public Class AdvancedEncryption

    ' تشفير AES مع مفاتيح مشتقة من العتاد
    Public Shared Function EncryptWithHardwareKey(data As String) As String
        Try
            Dim key As Byte() = DeriveKeyFromHardware()
            Dim iv As Byte() = GenerateSecureIV()

            Using aes As Aes = Aes.Create()
                aes.Key = key
                aes.IV = iv
                aes.Mode = CipherMode.CBC
                aes.Padding = PaddingMode.PKCS7

                Using encryptor As ICryptoTransform = aes.CreateEncryptor()
                    Dim dataBytes As Byte() = Encoding.UTF8.GetBytes(data)
                    Dim encryptedBytes As Byte() = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length)

                    ' دمج IV مع البيانات المشفرة
                    Dim result(iv.Length + encryptedBytes.Length - 1) As Byte
                    Array.Copy(iv, 0, result, 0, iv.Length)
                    Array.Copy(encryptedBytes, 0, result, iv.Length, encryptedBytes.Length)

                    Return Convert.ToBase64String(result)
                End Using
            End Using
        Catch ex As Exception
            Throw New SecurityException($"Encryption failed: {ex.Message}")
        End Try
    End Function

    ' فك تشفير AES مع مفاتيح مشتقة من العتاد
    Public Shared Function DecryptWithHardwareKey(encryptedData As String) As String
        Try
            Dim key As Byte() = DeriveKeyFromHardware()
            Dim combinedData As Byte() = Convert.FromBase64String(encryptedData)

            ' استخراج IV والبيانات المشفرة
            Dim iv(15) As Byte ' AES IV is 16 bytes
            Dim encryptedBytes(combinedData.Length - 17) As Byte

            Array.Copy(combinedData, 0, iv, 0, 16)
            Array.Copy(combinedData, 16, encryptedBytes, 0, encryptedBytes.Length)

            Using aes As Aes = Aes.Create()
                aes.Key = key
                aes.IV = iv
                aes.Mode = CipherMode.CBC
                aes.Padding = PaddingMode.PKCS7

                Using decryptor As ICryptoTransform = aes.CreateDecryptor()
                    Dim decryptedBytes As Byte() = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length)
                    Return Encoding.UTF8.GetString(decryptedBytes)
                End Using
            End Using
        Catch ex As Exception
            Throw New SecurityException($"Decryption failed: {ex.Message}")
        End Try
    End Function

    Private Shared Function DeriveKeyFromHardware() As Byte()
        Try
            ' اشتقاق مفتاح قوي من معلومات العتاد
            Dim hardwareInfo As String = GetStableHardwareFingerprint()
            Dim salt As Byte() = Encoding.UTF8.GetBytes("YourUniqueSalt2024!@#$%^&*()")

            Using pbkdf2 As New Rfc2898DeriveBytes(hardwareInfo, salt, 10000, HashAlgorithmName.SHA256)
                Return pbkdf2.GetBytes(32) ' 256-bit key
            End Using
        Catch ex As Exception
            ' في حالة فشل الحصول على معلومات العتاد، استخدم مفتاح احتياطي
            Return GetFallbackKey()
        End Try
    End Function

    Public Shared Function GetStableHardwareFingerprint() As String
        Dim fingerprint As New StringBuilder()

        Try
            ' معرف المعالج
            fingerprint.Append(GetProcessorID())
            fingerprint.Append("|")

            ' معرف اللوحة الأم
            fingerprint.Append(GetMotherboardID())
            fingerprint.Append("|")

            ' معرف القرص الصلب الأول
            fingerprint.Append(GetFirstHardDriveID())
            fingerprint.Append("|")

            ' معرف BIOS
            fingerprint.Append(GetBIOSSerialNumber())

        Catch ex As Exception
            ' في حالة فشل الحصول على أي معلومات، استخدم معرف النظام
            fingerprint.Append(Environment.MachineName)
            fingerprint.Append("|")
            fingerprint.Append(Environment.UserName)
        End Try

        ' تطبيق hash على البصمة النهائية
        Using sha256 As SHA256 = SHA256.Create()
            Dim hashBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint.ToString()))
            Return Convert.ToBase64String(hashBytes)
        End Using
    End Function

    Private Shared Function GetProcessorID() As String
        Try
            Using searcher As New ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor")
                For Each obj As ManagementObject In searcher.Get()
                    Dim processorId As Object = obj("ProcessorId")
                    If processorId IsNot Nothing Then
                        Return processorId.ToString()
                    End If
                Next
            End Using
        Catch
        End Try
        Return "UNKNOWN_CPU"
    End Function

    Private Shared Function GetMotherboardID() As String
        Try
            Using searcher As New ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard")
                For Each obj As ManagementObject In searcher.Get()
                    Dim serialNumber As Object = obj("SerialNumber")
                    If serialNumber IsNot Nothing AndAlso Not String.IsNullOrWhiteSpace(serialNumber.ToString()) Then
                        Return serialNumber.ToString()
                    End If
                Next
            End Using
        Catch
        End Try
        Return "UNKNOWN_MOBO"
    End Function

    Private Shared Function GetFirstHardDriveID() As String
        Try
            Using searcher As New ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE Index = 0")
                For Each obj As ManagementObject In searcher.Get()
                    Dim serialNumber As Object = obj("SerialNumber")
                    If serialNumber IsNot Nothing Then
                        Return serialNumber.ToString().Trim()
                    End If
                Next
            End Using
        Catch
        End Try
        Return "UNKNOWN_HDD"
    End Function

    Private Shared Function GetBIOSSerialNumber() As String
        Try
            Using searcher As New ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS")
                For Each obj As ManagementObject In searcher.Get()
                    Dim serialNumber As Object = obj("SerialNumber")
                    If serialNumber IsNot Nothing AndAlso Not String.IsNullOrWhiteSpace(serialNumber.ToString()) Then
                        Return serialNumber.ToString()
                    End If
                Next
            End Using
        Catch
        End Try
        Return "UNKNOWN_BIOS"
    End Function

    Private Shared Function GenerateSecureIV() As Byte()
        Dim iv(15) As Byte ' AES requires 16-byte IV
        Using rng As RandomNumberGenerator = RandomNumberGenerator.Create()
            rng.GetBytes(iv)
        End Using
        Return iv
    End Function

    Private Shared Function GetFallbackKey() As Byte()
        ' مفتاح احتياطي في حالة فشل الحصول على معلومات العتاد
        Dim fallbackString As String = $"{Environment.MachineName}|{Environment.UserName}|FALLBACK_2024"
        Using sha256 As SHA256 = SHA256.Create()
            Return sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackString))
        End Using
    End Function

    ' تشفير RSA للبيانات الحساسة (مبسط للتوافق مع .NET Framework 4.7.2)
    Public Shared Function EncryptWithRSA(data As String, publicKey As String) As String
        Try
            Using rsa As RSA = RSA.Create()
                ' مؤقت - يحتاج تنفيذ متوافق مع .NET Framework 4.7.2
                Dim dataBytes As Byte() = Encoding.UTF8.GetBytes(data)
                Dim encryptedBytes As Byte() = rsa.Encrypt(dataBytes, RSAEncryptionPadding.OaepSHA1)

                Return Convert.ToBase64String(encryptedBytes)
            End Using
        Catch ex As Exception
            Throw New SecurityException($"RSA encryption failed: {ex.Message}")
        End Try
    End Function

    ' فك تشفير RSA (مبسط للتوافق مع .NET Framework 4.7.2)
    Public Shared Function DecryptWithRSA(encryptedData As String, privateKey As String) As String
        Try
            Using rsa As RSA = RSA.Create()
                ' مؤقت - يحتاج تنفيذ متوافق مع .NET Framework 4.7.2
                Dim encryptedBytes As Byte() = Convert.FromBase64String(encryptedData)
                Dim decryptedBytes As Byte() = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA1)

                Return Encoding.UTF8.GetString(decryptedBytes)
            End Using
        Catch ex As Exception
            Throw New SecurityException($"RSA decryption failed: {ex.Message}")
        End Try
    End Function

    ' تشفير XOR بسيط للبيانات السريعة
    Public Shared Function XorEncrypt(data As Byte(), key As Byte()) As Byte()
        Dim result(data.Length - 1) As Byte
        For i As Integer = 0 To data.Length - 1
            result(i) = CByte(data(i) Xor key(i Mod key.Length))
        Next
        Return result
    End Function

    ' حساب SHA256 hash
    Public Shared Function ComputeSHA256(data As String) As String
        Using sha256 As SHA256 = SHA256.Create()
            Dim hashBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(data))
            Return Convert.ToBase64String(hashBytes)
        End Using
    End Function

    ' حساب SHA256 hash للملفات
    Public Shared Function ComputeFileSHA256(filePath As String) As String
        Using sha256 As SHA256 = SHA256.Create()
            Using fileStream As New IO.FileStream(filePath, IO.FileMode.Open, IO.FileAccess.Read)
                Dim hashBytes As Byte() = sha256.ComputeHash(fileStream)
                Return Convert.ToBase64String(hashBytes)
            End Using
        End Using
    End Function

End Class
