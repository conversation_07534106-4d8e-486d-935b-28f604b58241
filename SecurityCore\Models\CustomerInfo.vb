Public Class CustomerInfo
    Public Property CustomerId As String
    Public Property Name As String
    Public Property Email As String
    Public Property Company As String
    Public Property Phone As String
    Public Property Address As String
    Public Property Country As String
    Public Property RegistrationDate As DateTime
    Public Property LastLoginDate As DateTime?
    Public Property IsActive As Boolean
    Public Property LicenseType As String
    Public Property MaxDevices As Integer
    Public Property CurrentDevices As Integer
    Public Property Notes As String
    Public Property AdditionalFields As Dictionary(Of String, Object)
    
    Public Sub New()
        CustomerId = Guid.NewGuid().ToString()
        RegistrationDate = DateTime.UtcNow
        IsActive = True
        MaxDevices = 1
        CurrentDevices = 0
        AdditionalFields = New Dictionary(Of String, Object)
    End Sub
    
    Public Sub New(name As String, email As String)
        Me.New()
        Me.Name = name
        Me.Email = email
    End Sub
    
    Public Function ToJson() As String
        Return Newtonsoft.Json.JsonConvert.SerializeObject(Me, Newtonsoft.Json.Formatting.Indented)
    End Function
    
    Public Shared Function FromJson(json As String) As CustomerInfo
        Return Newtonsoft.Json.JsonConvert.DeserializeObject(Of CustomerInfo)(json)
    End Function
    
    Public Overrides Function ToString() As String
        Return $"{Name} ({Email}) - {LicenseType}"
    End Function
End Class
