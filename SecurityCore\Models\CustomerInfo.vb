' Imports Newtonsoft.Json - مؤقت

Public Class CustomerInfo
    Public Property CustomerId As String
    Public Property Name As String
    Public Property Email As String
    Public Property Company As String
    Public Property Phone As String
    Public Property Address As String
    Public Property Country As String
    Public Property RegistrationDate As DateTime
    Public Property LastLoginDate As DateTime?
    Public Property IsActive As Boolean
    Public Property LicenseType As String
    Public Property MaxDevices As Integer
    Public Property CurrentDevices As Integer
    Public Property Notes As String
    Public Property AdditionalFields As Dictionary(Of String, Object)

    Public Sub New()
        CustomerId = Guid.NewGuid().ToString()
        RegistrationDate = DateTime.UtcNow
        IsActive = True
        MaxDevices = 1
        CurrentDevices = 0
        AdditionalFields = New Dictionary(Of String, Object)
    End Sub

    Public Sub New(name As String, email As String)
        Me.New()
        Me.Name = name
        Me.Email = email
    End Sub

    Public Function ToJson() As String
        ' مؤقت - يجب استبداله بـ Newtonsoft.Json لاحقاً
        Return "{""placeholder"": ""json""}"
    End Function

    Public Shared Function FromJson(json As String) As CustomerInfo
        ' مؤقت - يجب استبداله بـ Newtonsoft.Json لاحقاً
        Return New CustomerInfo()
    End Function

    Public Overrides Function ToString() As String
        Return $"{Name} ({Email}) - {LicenseType}"
    End Function
End Class
