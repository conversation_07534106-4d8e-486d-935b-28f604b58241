Imports System.Net.Http
Imports System.Threading.Tasks
Imports System.IO
Imports Newtonsoft.Json

Public Class AdvancedAlertSystem
    
    Public Shared Async Function SendCriticalSecurityAlert(incident As SecurityIncident) As Task
        Try
            ' تحديث معلومات الحادث
            Await UpdateIncidentDetails(incident)
            
            ' إرسال تنبيهات متعددة القنوات
            Dim tasks As New List(Of Task) From {
                SendTelegramAlert(incident),
                SendEmailAlert(incident),
                LogToFirebase(incident)
            }
            
            ' انتظار جميع المهام مع timeout
            Await Task.WhenAll(tasks).ConfigureAwait(False)
            
            ' قناة 4: ملف محلي مشفر كنسخة احتياطية
            LogToEncryptedFile(incident)
            
        Catch ex As Exception
            ' في حالة فشل الإرسال، حفظ محلي على الأقل
            LogToEncryptedFile(incident)
            LogError($"Failed to send security alert: {ex.Message}")
        End Try
    End Function
    
    Private Shared Async Function UpdateIncidentDetails(incident As SecurityIncident) As Task
        Try
            ' تحديث معلومات الشبكة
            incident.LocalIP = Await GetLocalIPAddress()
            incident.PublicIP = Await GetPublicIPAddress()
            incident.Location = Await GetApproximateLocation(incident.PublicIP)
            
            ' تحديث بصمة العتاد
            incident.HardwareFingerprint = AdvancedEncryption.ComputeSHA256(
                AdvancedEncryption.GetStableHardwareFingerprint())
            
        Catch ex As Exception
            LogError($"Failed to update incident details: {ex.Message}")
        End Try
    End Function
    
    Private Shared Async Function SendTelegramAlert(incident As SecurityIncident) As Task
        Try
            Dim message As String = FormatSecurityMessage(incident)
            
            ' إرسال للإدمن الرئيسي
            Await SecureTelegramNotifier.SendToAdmin(message)
            
            ' إرسال لقناة التنبيهات إذا كان الحادث خطير
            If incident.Severity >= SecuritySeverity.High Then
                Await SecureTelegramNotifier.SendToChannel(message)
            End If
            
        Catch ex As Exception
            LogError($"Failed to send Telegram alert: {ex.Message}")
        End Try
    End Function
    
    Private Shared Async Function SendEmailAlert(incident As SecurityIncident) As Task
        Try
            ' تنفيذ إرسال البريد الإلكتروني (إذا كان متاح)
            ' يمكن إضافة هذه الوظيفة لاحقاً
            Await Task.Delay(1) ' placeholder
            
        Catch ex As Exception
            LogError($"Failed to send email alert: {ex.Message}")
        End Try
    End Function
    
    Private Shared Async Function LogToFirebase(incident As SecurityIncident) As Task
        Try
            ' تسجيل الحادث في Firebase
            Await SecureFirebaseClient.LogSecurityIncident(incident)
            
        Catch ex As Exception
            LogError($"Failed to log to Firebase: {ex.Message}")
        End Try
    End Function
    
    Private Shared Sub LogToEncryptedFile(incident As SecurityIncident)
        Try
            Dim logDir As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SecurityLogs")
            If Not Directory.Exists(logDir) Then
                Directory.CreateDirectory(logDir)
            End If
            
            Dim logFile As String = Path.Combine(logDir, $"security_{DateTime.Now:yyyyMM}.log")
            Dim logEntry As String = JsonConvert.SerializeObject(incident, Formatting.None)
            
            ' تشفير السجل
            Dim encryptedEntry As String = AdvancedEncryption.EncryptWithHardwareKey(logEntry)
            
            ' إضافة السجل للملف
            File.AppendAllText(logFile, encryptedEntry & Environment.NewLine)
            
        Catch ex As Exception
            LogError($"Failed to log to encrypted file: {ex.Message}")
        End Try
    End Sub
    
    Private Shared Function FormatSecurityMessage(incident As SecurityIncident) As String
        Dim severity As String = GetSeverityEmoji(incident.Severity)
        
        Return $"{severity} تنبيه أمني: {incident.Type}

📋 التفاصيل:
- الوقت: {incident.Timestamp:dd/MM/yyyy HH:mm:ss}
- نوع الحادث: {incident.Type}
- الوصف: {incident.Description}
- مستوى الخطورة: {incident.Severity}

🖥️ معلومات النظام:
- اسم الجهاز: {incident.MachineName}
- المستخدم: {incident.UserName}
- العملية: {incident.ProcessName}
- معرف العتاد: {incident.HardwareFingerprint?.Substring(0, Math.Min(16, incident.HardwareFingerprint?.Length ?? 0))}...

🌐 معلومات الشبكة:
- IP المحلي: {incident.LocalIP}
- IP العام: {incident.PublicIP}
- الموقع التقريبي: {incident.Location}

⚠️ الإجراء المتخذ: {incident.ActionTaken}"
    End Function
    
    Private Shared Function GetSeverityEmoji(severity As SecuritySeverity) As String
        Select Case severity
            Case SecuritySeverity.Low
                Return "🟡"
            Case SecuritySeverity.Medium
                Return "🟠"
            Case SecuritySeverity.High
                Return "🔴"
            Case SecuritySeverity.Critical
                Return "🚨"
            Case Else
                Return "⚪"
        End Select
    End Function
    
    Private Shared Async Function GetLocalIPAddress() As Task(Of String)
        Try
            Using client As New HttpClient()
                client.Timeout = TimeSpan.FromSeconds(5)
                ' استخدام خدمة للحصول على IP المحلي
                Return Await client.GetStringAsync("https://ipv4.icanhazip.com/")
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function
    
    Private Shared Async Function GetPublicIPAddress() As Task(Of String)
        Try
            Using client As New HttpClient()
                client.Timeout = TimeSpan.FromSeconds(5)
                Dim response As String = Await client.GetStringAsync("https://api.ipify.org")
                Return response.Trim()
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function
    
    Private Shared Async Function GetApproximateLocation(ipAddress As String) As Task(Of String)
        Try
            If String.IsNullOrWhiteSpace(ipAddress) OrElse ipAddress = "Unknown" Then
                Return "Unknown"
            End If
            
            Using client As New HttpClient()
                client.Timeout = TimeSpan.FromSeconds(10)
                ' استخدام خدمة مجانية للحصول على الموقع التقريبي
                Dim response As String = Await client.GetStringAsync($"http://ip-api.com/json/{ipAddress}")
                Dim locationData = JsonConvert.DeserializeObject(Of Dictionary(Of String, Object))(response)
                
                If locationData.ContainsKey("country") AndAlso locationData.ContainsKey("city") Then
                    Return $"{locationData("city")}, {locationData("country")}"
                End If
            End Using
        Catch
        End Try
        
        Return "Unknown"
    End Function
    
    ' إرسال تنبيه سريع للحوادث الحرجة
    Public Shared Async Function SendQuickAlert(alertType As String, message As String) As Task
        Try
            Dim incident As New SecurityIncident(alertType, message, SecuritySeverity.High)
            incident.ActionTaken = "Quick alert sent"
            
            Await SendTelegramAlert(incident)
            LogToEncryptedFile(incident)
            
        Catch ex As Exception
            LogError($"Failed to send quick alert: {ex.Message}")
        End Try
    End Function
    
    ' إرسال تنبيه حرج فوري
    Public Shared Sub SendEmergencyAlert(message As String)
        Try
            ' إرسال فوري دون انتظار
            Task.Run(Async Function()
                         Dim incident As New SecurityIncident("EMERGENCY", message, SecuritySeverity.Critical)
                         incident.ActionTaken = "Emergency shutdown initiated"
                         Await SendCriticalSecurityAlert(incident)
                     End Function)
        Catch ex As Exception
            LogError($"Failed to send emergency alert: {ex.Message}")
        End Try
    End Function
    
    Private Shared Sub LogError(message As String)
        Try
            ' تسجيل الأخطاء في ملف منفصل
            Dim errorLog As String = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "SecurityLogs", "errors.log")
            
            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}{Environment.NewLine}"
            File.AppendAllText(errorLog, logEntry)
            
        Catch
            ' تجاهل أخطاء التسجيل لتجنب التكرار اللانهائي
        End Try
    End Sub
    
End Class
