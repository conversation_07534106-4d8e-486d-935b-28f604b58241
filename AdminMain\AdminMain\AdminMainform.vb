﻿Imports SecurityCore
Imports SecurityCore.Security
Imports SecurityCore.Models
Imports SecurityCore.Telegram
Imports System.Threading.Tasks

Public Class AdminMainform

    Private _securityManager As AdvancedSecurityManager
    Private _alertSystem As AdvancedAlertSystem
    Private _isInitialized As Boolean = False

    ' أزرار الواجهة (تم إضافتها في Designer)

    Private Sub SetupFontsAndRendering()
        Try
            ' إعداد الخطوط الافتراضية
            Me.Font = New Font("Tahoma", 9, FontStyle.Regular)

            ' إعداد إعدادات الرسم
            Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or
                       ControlStyles.UserPaint Or
                       ControlStyles.DoubleBuffer Or
                       ControlStyles.ResizeRedraw, True)

            ' إعداد خصائص النموذج
            Me.AutoScaleMode = AutoScaleMode.Font
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            ' تطبيق الخط على جميع العناصر
            ApplyFontToAllControls(Me, Me.Font)

        Catch ex As Exception
            ' تجاهل أخطاء إعداد الخطوط
            Console.WriteLine($"تحذير: فشل في إعداد الخطوط: {ex.Message}")
        End Try
    End Sub

    Private Sub ApplyFontToAllControls(parent As Control, font As Font)
        Try
            For Each ctrl As Control In parent.Controls
                If TypeOf ctrl Is GroupBox OrElse
                   TypeOf ctrl Is Label OrElse
                   TypeOf ctrl Is Button OrElse
                   TypeOf ctrl Is TextBox Then
                    ctrl.Font = font
                End If

                ' تطبيق على العناصر الفرعية
                If ctrl.HasChildren Then
                    ApplyFontToAllControls(ctrl, font)
                End If
            Next
        Catch ex As Exception
            ' تجاهل أخطاء تطبيق الخطوط
            Console.WriteLine($"تحذير: فشل في تطبيق الخط: {ex.Message}")
        End Try
    End Sub

    Private Sub AdminMainform_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' إعداد الخطوط والرسم أولاً
            SetupFontsAndRendering()

            InitializeSecuritySystems()
            InitializeUI()
            LoadDashboardData()
        Catch ex As Exception
            MessageBox.Show($"خطأ في تهيئة النظام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub InitializeSecuritySystems()
        Try
            ' تهيئة أنظمة الأمان
            AdvancedSecurityManager.InitializeProductionSecurity()

            ' تهيئة نظام التنبيهات
            _alertSystem = New AdvancedAlertSystem()

            _isInitialized = True

            ' إرسال تنبيه بدء تشغيل النظام
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendToAdmin("🚀 تم تشغيل نظام الإدارة بنجاح")
                     End Function)

        Catch ex As Exception
            Throw New Exception($"فشل في تهيئة أنظمة الأمان: {ex.Message}")
        End Try
    End Sub

    Private Sub InitializeUI()
        Try
            ' تحديث عنوان النافذة
            Me.Text = $"نظام إدارة التراخيص - {DateTime.Now:yyyy-MM-dd}"

            ' تحديث حالة الواجهة
            UpdateUIStatus("جاهز للعمل")

        Catch ex As Exception
            Throw New Exception($"فشل في تهيئة الواجهة: {ex.Message}")
        End Try
    End Sub

    Private Sub LoadDashboardData()
        Try
            ' تحميل بيانات لوحة التحكم
            Task.Run(Async Function()
                         Await LoadLicenseStatistics()
                         Await LoadSecurityAlerts()
                         Await LoadSystemStatus()
                     End Function)

        Catch ex As Exception
            LogError($"فشل في تحميل بيانات لوحة التحكم: {ex.Message}")
        End Try
    End Sub

    Private Async Function LoadLicenseStatistics() As Task
        Try
            ' تحميل إحصائيات التراخيص من Firebase
            ' هذا مثال - يجب تنفيذه مع Firebase الفعلي

            Invoke(Sub()
                       ' تحديث الواجهة بالإحصائيات
                       UpdateLicenseStats(50, 45, 5, 3)
                   End Sub)

        Catch ex As Exception
            LogError($"فشل في تحميل إحصائيات التراخيص: {ex.Message}")
        End Try
    End Function

    Private Async Function LoadSecurityAlerts() As Task
        Try
            ' تحميل التنبيهات الأمنية الحديثة
            ' هذا مثال - يجب تنفيذه مع Firebase الفعلي

            Invoke(Sub()
                       ' تحديث قائمة التنبيهات
                       UpdateSecurityAlertsList()
                   End Sub)

        Catch ex As Exception
            LogError($"فشل في تحميل التنبيهات الأمنية: {ex.Message}")
        End Try
    End Function

    Private Async Function LoadSystemStatus() As Task
        Try
            ' فحص حالة النظام
            Dim systemStatus As String = "نظام سليم"
            Dim statusColor As Color = Color.Green

            ' فحص الاتصال بـ Firebase
            If Not Await TestFirebaseConnection() Then
                systemStatus = "مشكلة في الاتصال بقاعدة البيانات"
                statusColor = Color.Red
            End If

            ' فحص نظام التنبيهات
            If Not Await TestTelegramConnection() Then
                systemStatus = "مشكلة في نظام التنبيهات"
                statusColor = Color.Orange
            End If

            Invoke(Sub()
                       UpdateSystemStatus(systemStatus, statusColor)
                   End Sub)

        Catch ex As Exception
            LogError($"فشل في فحص حالة النظام: {ex.Message}")
        End Try
    End Function

    Private Async Function TestFirebaseConnection() As Task(Of Boolean)
        Try
            ' اختبار الاتصال بـ Firebase
            ' هذا مثال - يجب تنفيذه مع Firebase الفعلي
            Await Task.Delay(100) ' محاكاة الاختبار
            Return True
        Catch
            Return False
        End Try
    End Function

    Private Async Function TestTelegramConnection() As Task(Of Boolean)
        Try
            ' اختبار نظام التنبيهات
            Await SecureTelegramNotifier.SendToAdmin("اختبار الاتصال")
            Return True
        Catch
            Return False
        End Try
    End Function

    Private Sub UpdateUIStatus(status As String)
        ' تحديث شريط الحالة
        ' يجب إضافة StatusStrip للفورم
    End Sub

    Private Sub UpdateLicenseStats(total As Integer, active As Integer, expired As Integer, suspended As Integer)
        ' تحديث إحصائيات التراخيص في الواجهة
        ' يجب إضافة Labels أو Controls للإحصائيات
    End Sub

    Private Sub UpdateSecurityAlertsList()
        ' تحديث قائمة التنبيهات الأمنية
        ' يجب إضافة ListBox أو DataGridView للتنبيهات
    End Sub

    Private Sub UpdateSystemStatus(status As String, statusColor As Color)
        ' تحديث حالة النظام
        ' يجب إضافة Label لحالة النظام
    End Sub

    ' أحداث الأزرار والقوائم
    Private Sub btnCreateLicense_Click(sender As Object, e As EventArgs) Handles btnCreateLicense.Click
        Try
            ' فتح نافذة إنشاء ترخيص جديد
            Dim createForm As New CreateLicenseForm()
            If createForm.ShowDialog() = DialogResult.OK Then
                ' تحديث البيانات بعد إنشاء الترخيص
                LoadDashboardData()
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء الترخيص: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnManageLicenses_Click(sender As Object, e As EventArgs) Handles btnManageLicenses.Click
        Try
            ' فتح نافذة إدارة التراخيص
            Dim manageForm As New ManageLicensesForm()
            manageForm.ShowDialog()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح إدارة التراخيص: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSecurityReports_Click(sender As Object, e As EventArgs) Handles btnSecurityReports.Click
        Try
            ' فتح نافذة التقارير الأمنية
            Dim reportsForm As New SecurityReportsForm()
            reportsForm.ShowDialog()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح التقارير الأمنية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSystemSettings_Click(sender As Object, e As EventArgs) Handles btnSystemSettings.Click
        Try
            ' فتح نافذة إعدادات النظام
            Dim settingsForm As New SystemSettingsForm()
            settingsForm.ShowDialog()
        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح إعدادات النظام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub AdminMainform_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            ' إرسال تنبيه إغلاق النظام
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendToAdmin("🔴 تم إغلاق نظام الإدارة")
                     End Function)

            ' تنظيف الموارد
            CleanupResources()

        Catch ex As Exception
            LogError($"خطأ في إغلاق النظام: {ex.Message}")
        End Try
    End Sub

    Private Sub CleanupResources()
        Try
            ' تنظيف موارد النظام
            SecureTelegramNotifier.Dispose()
        Catch ex As Exception
            LogError($"خطأ في تنظيف الموارد: {ex.Message}")
        End Try
    End Sub

    Private Sub LogError(message As String)
        Try
            ' تسجيل الأخطاء
            Console.WriteLine($"[ERROR] {DateTime.Now}: {message}")

            ' يمكن إضافة تسجيل في ملف أو قاعدة بيانات
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    ' معالجة أخطاء الرسم
    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        Try
            MyBase.OnPaint(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم المتعلقة بالخطوط
            Console.WriteLine($"تحذير: خطأ في الرسم: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في الرسم: {ex.Message}")
        End Try
    End Sub

    Protected Overrides Sub OnPaintBackground(e As PaintEventArgs)
        Try
            MyBase.OnPaintBackground(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم
            Console.WriteLine($"تحذير: خطأ في رسم الخلفية: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في رسم الخلفية: {ex.Message}")
        End Try
    End Sub

End Class