Imports System.Runtime.InteropServices
Imports System.Text

Public Class NativeMethods

    ' Kernel32.dll functions
    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function IsDebuggerPresent() As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function CheckRemoteDebuggerPresent(hProcess As IntPtr, <Out> ByRef pbDebuggerPresent As Boolean) As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function GetCurrentThread() As IntPtr
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function GetThreadContext(hThread As IntPtr, ByRef lpContext As CONTEXT) As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function VirtualProtect(lpAddress As IntPtr, dwSize As UIntPtr, flNewProtect As UInteger, <Out> ByRef lpflOldProtect As UInteger) As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True, CharSet:=CharSet.Unicode)>
    Public Shared Function GetModuleHandle(lpModuleName As String) As IntPtr
    End Function

    <DllImport("kernel32.dll", SetLastError:=True, CharSet:=CharSet.Ansi)>
    Public Shared Function GetProcAddress(hModule As IntPtr, lpProcName As String) As IntPtr
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function QueryFullProcessImageName(hProcess As IntPtr, dwFlags As UInteger, lpExeName As StringBuilder, ByRef lpdwSize As UInteger) As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Sub SetLastError(dwErrCode As UInteger)
    End Sub

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function GetLastError() As UInteger
    End Function

    <DllImport("kernel32.dll", SetLastError:=True, CharSet:=CharSet.Ansi)>
    Public Shared Sub OutputDebugStringA(lpOutputString As String)
    End Sub

    ' ntdll.dll functions
    <DllImport("ntdll.dll", SetLastError:=True)>
    Public Shared Function NtQueryInformationProcess(processHandle As IntPtr, processInformationClass As Integer, processInformation As IntPtr, processInformationLength As UInteger, <Out> ByRef returnLength As UInteger) As Integer
    End Function

    ' user32.dll functions
    <DllImport("user32.dll", SetLastError:=True)>
    Public Shared Function FindWindow(lpClassName As String, lpWindowName As String) As IntPtr
    End Function

    <DllImport("user32.dll", SetLastError:=True)>
    Public Shared Function GetWindowText(hWnd As IntPtr, lpString As StringBuilder, nMaxCount As Integer) As Integer
    End Function

    <DllImport("user32.dll", SetLastError:=True)>
    Public Shared Function GetWindowThreadProcessId(hWnd As IntPtr, <Out> ByRef lpdwProcessId As UInteger) As UInteger
    End Function

    ' advapi32.dll functions
    <DllImport("advapi32.dll", SetLastError:=True)>
    Public Shared Function OpenProcessToken(ProcessHandle As IntPtr, DesiredAccess As UInteger, <Out> ByRef TokenHandle As IntPtr) As Boolean
    End Function

    <DllImport("advapi32.dll", SetLastError:=True)>
    Public Shared Function GetTokenInformation(TokenHandle As IntPtr, TokenInformationClass As Integer, TokenInformation As IntPtr, TokenInformationLength As UInteger, <Out> ByRef ReturnLength As UInteger) As Boolean
    End Function

End Class

' Structure definitions
<StructLayout(LayoutKind.Sequential)>
Public Structure CONTEXT
    Public ContextFlags As UInteger
    Public Dr0 As UIntPtr
    Public Dr1 As UIntPtr
    Public Dr2 As UIntPtr
    Public Dr3 As UIntPtr
    Public Dr6 As UIntPtr
    Public Dr7 As UIntPtr
    ' يمكن إضافة المزيد من الحقول حسب الحاجة
End Structure

' Constants
Public Class Win32Constants
    Public Const CONTEXT_DEBUG_REGISTERS As UInteger = &H10UI
    Public Const PAGE_EXECUTE_READ As UInteger = &H20UI
    Public Const PAGE_EXECUTE_READWRITE As UInteger = &H40UI
    Public Const PAGE_READONLY As UInteger = &H2UI
    Public Const PAGE_READWRITE As UInteger = &H4UI

    ' Process access rights
    Public Const PROCESS_QUERY_INFORMATION As UInteger = &H400UI
    Public Const PROCESS_VM_READ As UInteger = &H10UI

    ' Token access rights
    Public Const TOKEN_QUERY As UInteger = &H8UI

    ' Process information classes
    Public Const ProcessDebugPort As Integer = 7
    Public Const ProcessDebugFlags As Integer = 31
    Public Const ProcessDebugObjectHandle As Integer = 30
End Class
