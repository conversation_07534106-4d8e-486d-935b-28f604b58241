Imports System.Runtime.InteropServices

Public Class Kernel32

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function IsDebuggerPresent() As Boolean
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Sub SetLastError(dwErrCode As UInteger)
    End Sub

    <DllImport("kernel32.dll", SetLastError:=True)>
    Public Shared Function GetLastError() As UInteger
    End Function

    <DllImport("kernel32.dll", SetLastError:=True, CharSet:=CharSet.Ansi)>
    Public Shared Sub OutputDebugStringA(lpOutputString As String)
    End Sub

End Class
