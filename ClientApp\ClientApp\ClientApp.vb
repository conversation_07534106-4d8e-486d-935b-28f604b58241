﻿Imports SecurityCore
Imports SecurityCore.Security
Imports SecurityCore.Models
Imports SecurityCore.Telegram
Imports System.Threading.Tasks
Imports System.Threading

Public Class ClientApp

    Private _securityMonitoringTimer As System.Windows.Forms.Timer
    Private _isSecurityActive As Boolean = False
    Private _lastSecurityCheck As DateTime = DateTime.MinValue
    Private _securityCheckInterval As Integer = 30000 ' 30 ثانية

    ' أزرار الواجهة (تم إضافتها في Designer)

    Private Sub SetupFontsAndRendering()
        Try
            ' إعداد الخطوط الافتراضية
            Me.Font = New Font("Tahoma", 9, FontStyle.Regular)

            ' إعداد إعدادات الرسم
            Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or
                       ControlStyles.UserPaint Or
                       ControlStyles.DoubleBuffer Or
                       ControlStyles.ResizeRedraw, True)

            ' إعداد خصائص النموذج
            Me.AutoScaleMode = AutoScaleMode.Font
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            ' تطبيق الخط على جميع العناصر
            ApplyFontToAllControls(Me, Me.Font)

        Catch ex As Exception
            ' تجاهل أخطاء إعداد الخطوط
            Console.WriteLine($"تحذير: فشل في إعداد الخطوط: {ex.Message}")
        End Try
    End Sub

    Private Sub ApplyFontToAllControls(parent As Control, font As Font)
        Try
            For Each ctrl As Control In parent.Controls
                If TypeOf ctrl Is GroupBox OrElse
                   TypeOf ctrl Is Label OrElse
                   TypeOf ctrl Is Button OrElse
                   TypeOf ctrl Is TextBox OrElse
                   TypeOf ctrl Is MenuStrip OrElse
                   TypeOf ctrl Is StatusStrip Then
                    ctrl.Font = font
                End If

                ' تطبيق على العناصر الفرعية
                If ctrl.HasChildren Then
                    ApplyFontToAllControls(ctrl, font)
                End If
            Next
        Catch ex As Exception
            ' تجاهل أخطاء تطبيق الخطوط
            Console.WriteLine($"تحذير: فشل في تطبيق الخط: {ex.Message}")
        End Try
    End Sub

    Private Sub ClientApp_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' إعداد الخطوط والرسم أولاً
            SetupFontsAndRendering()

            ' تهيئة التطبيق الرئيسي
            InitializeMainApplication()

            ' بدء مراقبة الأمان المستمرة
            StartSecurityMonitoring()

            ' تحديث واجهة المستخدم
            UpdateUserInterface()

        Catch ex As Exception
            MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Me.Close()
        End Try
    End Sub

    Private Sub InitializeMainApplication()
        Try
            ' تحديث عنوان النافذة
            Me.Text = $"تطبيق العميل - مرخص لـ {Environment.UserName}"

            ' تحديث خصائص النافذة
            Me.WindowState = FormWindowState.Maximized
            Me.BackColor = Color.FromArgb(245, 245, 245)

            ' إضافة معلومات الحالة
            UpdateStatusBar("تم تسجيل الدخول بنجاح")

        Catch ex As Exception
            Throw New Exception($"فشل في تهيئة التطبيق الرئيسي: {ex.Message}")
        End Try
    End Sub

    Private Sub StartSecurityMonitoring()
        Try
            ' إنشاء مؤقت مراقبة الأمان
            _securityMonitoringTimer = New System.Windows.Forms.Timer()
            _securityMonitoringTimer.Interval = _securityCheckInterval
            AddHandler _securityMonitoringTimer.Tick, AddressOf SecurityMonitoringTick

            ' بدء المراقبة
            _securityMonitoringTimer.Start()
            _isSecurityActive = True

            Console.WriteLine("تم بدء مراقبة الأمان المستمرة")

        Catch ex As Exception
            Throw New Exception($"فشل في بدء مراقبة الأمان: {ex.Message}")
        End Try
    End Sub

    Private Async Sub SecurityMonitoringTick(sender As Object, e As EventArgs)
        Try
            ' تجنب التداخل في الفحوصات
            If DateTime.Now.Subtract(_lastSecurityCheck).TotalSeconds < 25 Then
                Return
            End If

            _lastSecurityCheck = DateTime.Now

            ' تنفيذ فحوصات الأمان في خيط منفصل
            Await Task.Run(AddressOf PerformSecurityChecks)

        Catch ex As Exception
            Console.WriteLine($"خطأ في مراقبة الأمان: {ex.Message}")

            ' في حالة خطأ حرج، إغلاق التطبيق
            If ex.GetType() = GetType(SecurityException) Then
                Task.Run(Async Function()
                             Await HandleSecurityThreat("خطأ حرج في مراقبة الأمان")
                         End Function)
            End If
        End Try
    End Sub

    Private Async Function PerformSecurityChecks() As Task
        Try
            ' فحص Code Caves
            If AdvancedAntiCrackingMeasures.DetectCodeCaves() Then
                Await HandleSecurityThreat("تم اكتشاف Code Caves")
                Return
            End If

            ' فحص API Hooks
            If AdvancedAntiCrackingMeasures.DetectAPIHooks() Then
                Await HandleSecurityThreat("تم اكتشاف API Hooks")
                Return
            End If

            ' فحص Hardware Breakpoints
            If AdvancedAntiCrackingMeasures.DetectHardwareBreakpoints() Then
                Await HandleSecurityThreat("تم اكتشاف Hardware Breakpoints")
                Return
            End If

            ' فحص Process Hollowing
            If AdvancedAntiCrackingMeasures.DetectProcessHollowing() Then
                Await HandleSecurityThreat("تم اكتشاف Process Hollowing")
                Return
            End If

            ' التحقق من صحة الترخيص دورياً (كل 5 دقائق)
            If DateTime.Now.Minute Mod 5 = 0 AndAlso DateTime.Now.Second < 30 Then
                Await PerformPeriodicLicenseCheck()
            End If

            ' تحديث حالة الأمان في الواجهة
            Invoke(Sub() UpdateSecurityStatus("آمن"))

        Catch ex As Exception
            Task.Run(Async Function()
                         Await HandleSecurityThreat($"خطأ في فحوصات الأمان: {ex.Message}")
                     End Function)
        End Try
    End Function

    Private Async Function PerformPeriodicLicenseCheck() As Task
        Try
            Dim validationResult As ValidationResult = Await Task.Run(Function() ProductionLicenseValidator.ValidateProductionLicense())

            If Not validationResult.IsValid Then
                Await HandleSecurityThreat($"فشل التحقق الدوري من الترخيص: {validationResult.ErrorMessage}")
            Else
                Console.WriteLine("تم التحقق الدوري من الترخيص بنجاح")
            End If

        Catch ex As Exception
            Task.Run(Async Function()
                         Await HandleSecurityThreat($"خطأ في التحقق الدوري من الترخيص: {ex.Message}")
                     End Function)
        End Try
    End Function

    Private Async Function HandleSecurityThreat(threatDescription As String) As Task
        Try
            ' تسجيل التهديد
            Console.WriteLine($"[SECURITY THREAT] {threatDescription}")

            ' إرسال تنبيه أمني فوري
            Dim incident As New SecurityIncident("Security Threat Detected", threatDescription, SecuritySeverity.Critical)
            incident.ActionTaken = "تم إغلاق التطبيق فوراً"

            Await AdvancedAlertSystem.SendCriticalSecurityAlert(incident)

            ' إرسال تنبيه طوارئ
            Await SecureTelegramNotifier.SendEmergencyAlert($"تهديد أمني في تطبيق العميل: {threatDescription}")

            ' إغلاق التطبيق فوراً
            Invoke(Sub()
                       MessageBox.Show("تم اكتشاف تهديد أمني. سيتم إغلاق التطبيق.", "تنبيه أمني", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                       Application.Exit()
                   End Sub)

        Catch ex As Exception
            ' في حالة فشل إرسال التنبيه، إغلاق التطبيق مباشرة
            Console.WriteLine($"فشل في معالجة التهديد الأمني: {ex.Message}")
            Environment.Exit(-1)
        End Try
    End Function

    Private Sub UpdateUserInterface()
        Try
            ' تحديث واجهة المستخدم الرئيسية
            Invoke(Sub()
                       ' إضافة معلومات المستخدم
                       UpdateUserInfo()

                       ' إضافة معلومات الترخيص
                       UpdateLicenseInfo()

                       ' إضافة حالة الأمان
                       UpdateSecurityStatus("نشط")
                   End Sub)

        Catch ex As Exception
            Console.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateUserInfo()
        Try
            ' تحديث معلومات المستخدم في الواجهة
            ' يجب إضافة Labels للمعلومات في Designer

        Catch ex As Exception
            Console.WriteLine($"خطأ في تحديث معلومات المستخدم: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateLicenseInfo()
        Try
            ' تحديث معلومات الترخيص في الواجهة
            ' يجب إضافة Labels للمعلومات في Designer

        Catch ex As Exception
            Console.WriteLine($"خطأ في تحديث معلومات الترخيص: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateSecurityStatus(status As String)
        Try
            ' تحديث حالة الأمان في الواجهة
            ' يجب إضافة Label لحالة الأمان في Designer

        Catch ex As Exception
            Console.WriteLine($"خطأ في تحديث حالة الأمان: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateStatusBar(message As String)
        Try
            ' تحديث شريط الحالة
            ' يجب إضافة StatusStrip في Designer

        Catch ex As Exception
            Console.WriteLine($"خطأ في تحديث شريط الحالة: {ex.Message}")
        End Try
    End Sub

    ' أحداث القوائم والأزرار
    Private Sub btnFeature1_Click(sender As Object, e As EventArgs) Handles btnFeature1.Click
        Try
            ' تنفيذ الميزة الأولى
            MessageBox.Show("الميزة الأولى تعمل بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في تنفيذ الميزة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnFeature2_Click(sender As Object, e As EventArgs) Handles btnFeature2.Click
        Try
            ' تنفيذ الميزة الثانية
            MessageBox.Show("الميزة الثانية تعمل بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في تنفيذ الميزة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSettings_Click(sender As Object, e As EventArgs) Handles btnSettings.Click
        Try
            ' فتح نافذة الإعدادات
            Dim settingsForm As New ClientSettingsForm()
            settingsForm.ShowDialog()

        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnAbout_Click(sender As Object, e As EventArgs) Handles btnAbout.Click
        Try
            ' عرض معلومات حول التطبيق
            Dim aboutMessage As String = $"تطبيق العميل المحمي
الإصدار: {GetApplicationVersion()}
مرخص لـ: {Environment.UserName}
الجهاز: {Environment.MachineName}
نظام التشغيل: {Environment.OSVersion}

© 2025 - جميع الحقوق محفوظة"

            MessageBox.Show(aboutMessage, "حول التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في عرض معلومات التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClientApp_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            ' إيقاف مراقبة الأمان
            StopSecurityMonitoring()

            ' إرسال تنبيه إغلاق التطبيق
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendToAdmin($"🔴 تم إغلاق التطبيق الرئيسي للعميل - {Environment.UserName}")
                     End Function)

            ' تنظيف الموارد
            CleanupResources()

        Catch ex As Exception
            Console.WriteLine($"خطأ في إغلاق التطبيق: {ex.Message}")
        End Try
    End Sub

    Private Sub StopSecurityMonitoring()
        Try
            If _securityMonitoringTimer IsNot Nothing Then
                _securityMonitoringTimer.Stop()
                _securityMonitoringTimer.Dispose()
                _securityMonitoringTimer = Nothing
            End If

            _isSecurityActive = False
            Console.WriteLine("تم إيقاف مراقبة الأمان")

        Catch ex As Exception
            Console.WriteLine($"خطأ في إيقاف مراقبة الأمان: {ex.Message}")
        End Try
    End Sub

    Private Sub CleanupResources()
        Try
            ' تنظيف موارد النظام
            GC.Collect()
            GC.WaitForPendingFinalizers()
            GC.Collect()

        Catch ex As Exception
            Console.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}")
        End Try
    End Sub

    ' دوال مساعدة
    Private Function GetApplicationVersion() As String
        Try
            Return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
        Catch
            Return "غير معروف"
        End Try
    End Function

    ' معالج الأخطاء العامة
    Private Sub ClientApp_UnhandledException(sender As Object, e As UnhandledExceptionEventArgs)
        Try
            Dim ex As Exception = CType(e.ExceptionObject, Exception)

            ' تسجيل الخطأ
            Console.WriteLine($"[UNHANDLED EXCEPTION] {ex.Message}")

            ' إرسال تنبيه أمني
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendEmergencyAlert($"خطأ غير معالج في تطبيق العميل: {ex.Message}")
                     End Function)

            ' إغلاق التطبيق بأمان
            Environment.Exit(-1)

        Catch
            ' في حالة فشل معالجة الخطأ، إغلاق فوري
            Environment.Exit(-1)
        End Try
    End Sub

    ' حماية إضافية ضد التلاعب
    Private Sub ProtectAgainstTampering()
        Try
            ' فحص تكامل الملف التنفيذي
            Dim currentPath As String = System.Reflection.Assembly.GetExecutingAssembly().Location
            Dim currentHash As String = AdvancedEncryption.ComputeFileSHA256(currentPath)

            ' مقارنة مع الهاش المتوقع (يجب حفظه بشكل آمن)
            ' هذا مثال - يجب تنفيذه بشكل أكثر تطوراً

        Catch ex As Exception
            ' في حالة فشل فحص التكامل، إغلاق التطبيق
            Task.Run(Async Function()
                         Await HandleSecurityThreat($"فشل فحص تكامل الملف: {ex.Message}")
                     End Function)
        End Try
    End Sub

    ' معالجة أخطاء الرسم
    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        Try
            MyBase.OnPaint(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم المتعلقة بالخطوط
            Console.WriteLine($"تحذير: خطأ في الرسم: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في الرسم: {ex.Message}")
        End Try
    End Sub

    Protected Overrides Sub OnPaintBackground(e As PaintEventArgs)
        Try
            MyBase.OnPaintBackground(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم
            Console.WriteLine($"تحذير: خطأ في رسم الخلفية: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في رسم الخلفية: {ex.Message}")
        End Try
    End Sub

End Class