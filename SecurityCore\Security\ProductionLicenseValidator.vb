Imports System.IO
Imports System.Threading.Tasks
Imports Newtonsoft.Json

Public Class ProductionLicenseValidator
    ' مدقق الترخيص للإنتاج مع حماية متقدمة
    
    Private Shared ReadOnly _licenseFileName As String = "license.dat"
    Private Shared ReadOnly _lastValidationFileName As String = "lastvalidation.dat"
    Private Shared ReadOnly _offlineGracePeriodHours As Integer = 24
    
    Public Shared Function ValidateProductionLicense() As ValidationResult
        Try
            ' التحقق من البيئة أولاً
            If Not IsProductionEnvironment() Then
                Return New ValidationResult(False, "بيئة غير صالحة")
            End If
            
            ' فحص الأمان قبل التحقق من الترخيص
            If Not PerformSecurityPreCheck() Then
                Return New ValidationResult(False, "فشل في فحوصات الأمان الأولية")
            End If
            
            ' التحقق من الترخيص المحلي
            Dim localValidation As ValidationResult = ValidateLocalLicense()
            If Not localValidation.IsValid Then
                Return localValidation
            End If
            
            ' التحقق مع الخادم (إذا كان متاح)
            Dim serverValidation As ValidationResult = ValidateWithServerSync()
            If Not serverValidation.IsValid Then
                ' في حالة فشل الاتصال، التحقق من إمكانية العمل بدون اتصال
                If CanWorkOffline() Then
                    Return New ValidationResult(True, "وضع عدم الاتصال - مسموح مؤقتاً")
                Else
                    Return serverValidation
                End If
            End If
            
            ' تسجيل الدخول الناجح
            LogSuccessfulValidation()
            
            Return New ValidationResult(True, "تم التحقق بنجاح")
            
        Catch ex As Exception
            LogSecurityEvent($"License validation error: {ex.Message}")
            Return New ValidationResult(False, "خطأ في التحقق")
        End Try
    End Function
    
    Private Shared Function IsProductionEnvironment() As Boolean
        Try
            ' التحقق من أن التطبيق يعمل في بيئة إنتاج صحيحة
            
            ' فحص التوقيع الرقمي
            If Not VerifyDigitalSignature() Then Return False
            
            ' فحص مسار التثبيت
            If Not IsValidInstallationPath() Then Return False
            
            ' فحص مفاتيح الريجستري
            If Not CheckRegistryKeys() Then Return False
            
            Return True
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function VerifyDigitalSignature() As Boolean
        Try
            ' التحقق من التوقيع الرقمي للملف التنفيذي
            Dim currentPath As String = System.Reflection.Assembly.GetExecutingAssembly().Location
            
            ' هذا مثال - يجب تنفيذه مع التوقيع الرقمي الفعلي
            Return File.Exists(currentPath)
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function IsValidInstallationPath() As Boolean
        Try
            ' التحقق من مسار التثبيت الصحيح
            Dim currentPath As String = System.Reflection.Assembly.GetExecutingAssembly().Location
            Dim validPaths() As String = {
                Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
                Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86)
            }
            
            For Each validPath In validPaths
                If currentPath.StartsWith(validPath, StringComparison.OrdinalIgnoreCase) Then
                    Return True
                End If
            Next
            
            Return False
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function CheckRegistryKeys() As Boolean
        Try
            ' فحص مفاتيح الريجستري المطلوبة
            ' هذا مثال - يجب تخصيصه حسب التطبيق
            Return True
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function PerformSecurityPreCheck() As Boolean
        Try
            ' فحوصات أمان أولية
            If NativeMethods.IsDebuggerPresent() Then
                LogSecurityEvent("Debugger detected during license validation")
                Return False
            End If
            
            If AdvancedAntiCrackingMeasures.DetectCodeCaves() Then
                LogSecurityEvent("Code caves detected during license validation")
                Return False
            End If
            
            Return True
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function ValidateLocalLicense() As ValidationResult
        Try
            ' قراءة ملف الترخيص المشفر
            Dim licenseData As Byte() = ReadEncryptedLicenseFile()
            If licenseData Is Nothing Then
                Return New ValidationResult(False, "ملف الترخيص غير موجود")
            End If
            
            ' فك تشفير الترخيص
            Dim license As License = DecryptLicense(licenseData)
            If license Is Nothing Then
                Return New ValidationResult(False, "ملف ترخيص تالف")
            End If
            
            ' التحقق من التوقيع الرقمي
            If Not VerifyLicenseSignature(license) Then
                Return New ValidationResult(False, "ترخيص غير صحيح")
            End If
            
            ' التحقق من معرف العتاد
            Dim currentFingerprint As String = GetHardwareFingerprint()
            If Not license.HardwareFingerprint.Equals(currentFingerprint) Then
                LogSecurityEvent("Hardware fingerprint mismatch")
                Return New ValidationResult(False, "الترخيص غير صالح لهذا الجهاز")
            End If
            
            ' التحقق من تاريخ انتهاء الصلاحية
            If license.ExpiryDate <= DateTime.UtcNow Then
                Return New ValidationResult(False, "انتهت صلاحية الترخيص")
            End If
            
            ' التحقق من الحالة
            If Not license.IsActive Then
                Return New ValidationResult(False, "الترخيص غير نشط")
            End If
            
            Return New ValidationResult(True, "ترخيص محلي صحيح")
            
        Catch ex As Exception
            LogSecurityEvent($"Local license validation error: {ex.Message}")
            Return New ValidationResult(False, "خطأ في التحقق المحلي")
        End Try
    End Function
    
    Private Shared Function ReadEncryptedLicenseFile() As Byte()
        Try
            Dim licenseFilePath As String = GetLicenseFilePath()
            If Not File.Exists(licenseFilePath) Then
                Return Nothing
            End If
            
            Return File.ReadAllBytes(licenseFilePath)
        Catch
            Return Nothing
        End Try
    End Function
    
    Private Shared Function GetLicenseFilePath() As String
        Dim appDataPath As String = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
        Dim licenseDir As String = Path.Combine(appDataPath, "ClientApp", "License")
        
        If Not Directory.Exists(licenseDir) Then
            Directory.CreateDirectory(licenseDir)
        End If
        
        Return Path.Combine(licenseDir, _licenseFileName)
    End Function
    
    Private Shared Function DecryptLicense(encryptedData As Byte()) As License
        Try
            Dim encryptedString As String = System.Text.Encoding.UTF8.GetString(encryptedData)
            Dim decryptedJson As String = AdvancedEncryption.DecryptWithHardwareKey(encryptedString)
            
            Return JsonConvert.DeserializeObject(Of License)(decryptedJson)
        Catch
            Return Nothing
        End Try
    End Function
    
    Private Shared Function VerifyLicenseSignature(license As License) As Boolean
        Try
            ' التحقق من التوقيع الرقمي للترخيص
            ' هذا مثال - يجب تنفيذه مع RSA أو تقنية توقيع أخرى
            Return Not String.IsNullOrEmpty(license.Signature)
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function GetHardwareFingerprint() As String
        Try
            ' الحصول على بصمة العتاد الحالية
            Return AdvancedEncryption.ComputeSHA256("HardwareFingerprint")
        Catch
            Return "UNKNOWN"
        End Try
    End Function
    
    Private Shared Function ValidateWithServerSync() As ValidationResult
        Try
            ' محاولة التحقق مع الخادم (نسخة متزامنة)
            Dim task As Task(Of ValidationResult) = ValidateWithServerAsync()
            task.Wait(10000) ' انتظار 10 ثوان كحد أقصى
            
            If task.IsCompleted Then
                Return task.Result
            Else
                Return New ValidationResult(False, "انتهت مهلة الاتصال بالخادم")
            End If
            
        Catch ex As Exception
            LogSecurityEvent($"Server validation error: {ex.Message}")
            Return New ValidationResult(False, "فشل الاتصال بالخادم")
        End Try
    End Function
    
    Private Shared Async Function ValidateWithServerAsync() As Task(Of ValidationResult)
        Try
            ' إنشاء عميل آمن للاتصال
            Using client As New System.Net.Http.HttpClient()
                client.Timeout = TimeSpan.FromSeconds(10)
                
                ' تحضير بيانات التحقق
                Dim validationRequest As New Dictionary(Of String, Object) From {
                    {"LicenseId", GetLocalLicenseId()},
                    {"HardwareFingerprint", GetHardwareFingerprint()},
                    {"Timestamp", DateTime.UtcNow},
                    {"ApplicationVersion", GetApplicationVersion()},
                    {"Checksum", CalculateRequestChecksum()}
                }
                
                ' تشفير الطلب
                Dim requestJson As String = JsonConvert.SerializeObject(validationRequest)
                Dim encryptedRequest As String = AdvancedEncryption.EncryptWithHardwareKey(requestJson)
                
                ' إرسال طلب التحقق (مثال)
                ' هذا يحتاج لتنفيذ مع Firebase أو خادم آخر
                Await Task.Delay(100) ' محاكاة الطلب
                
                ' تحديث آخر تحقق ناجح
                UpdateLastValidationTime()
                
                Return New ValidationResult(True, "تم التحقق مع الخادم")
                
        Catch ex As Exception
            LogSecurityEvent($"Server validation error: {ex.Message}")
            Return New ValidationResult(False, $"خطأ في التحقق مع الخادم: {ex.Message}")
        End Try
    End Function
    
    Private Shared Function GetLocalLicenseId() As String
        Try
            ' الحصول على معرف الترخيص المحلي
            Return "LOCAL_LICENSE_ID"
        Catch
            Return "UNKNOWN"
        End Try
    End Function
    
    Private Shared Function GetApplicationVersion() As String
        Try
            Return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
        Catch
            Return "UNKNOWN"
        End Try
    End Function
    
    Private Shared Function CalculateRequestChecksum() As String
        Try
            ' حساب checksum للطلب
            Dim data As String = $"{GetLocalLicenseId()}{GetHardwareFingerprint()}{DateTime.UtcNow:yyyyMMddHH}"
            Return AdvancedEncryption.ComputeSHA256(data)
        Catch
            Return "UNKNOWN"
        End Try
    End Function
    
    Private Shared Function CanWorkOffline() As Boolean
        Try
            ' التحقق من إمكانية العمل بدون اتصال
            Dim lastValidationTime As DateTime = GetLastValidationTime()
            Dim hoursSinceLastValidation As Double = DateTime.UtcNow.Subtract(lastValidationTime).TotalHours
            
            Return hoursSinceLastValidation <= _offlineGracePeriodHours
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Function GetLastValidationTime() As DateTime
        Try
            Dim validationFilePath As String = GetLastValidationFilePath()
            If Not File.Exists(validationFilePath) Then
                Return DateTime.MinValue
            End If
            
            Dim encryptedData As String = File.ReadAllText(validationFilePath)
            Dim decryptedData As String = AdvancedEncryption.DecryptWithHardwareKey(encryptedData)
            
            Return DateTime.Parse(decryptedData)
        Catch
            Return DateTime.MinValue
        End Try
    End Function
    
    Private Shared Function GetLastValidationFilePath() As String
        Dim appDataPath As String = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
        Dim licenseDir As String = Path.Combine(appDataPath, "ClientApp", "License")
        
        If Not Directory.Exists(licenseDir) Then
            Directory.CreateDirectory(licenseDir)
        End If
        
        Return Path.Combine(licenseDir, _lastValidationFileName)
    End Function
    
    Private Shared Sub UpdateLastValidationTime()
        Try
            Dim validationFilePath As String = GetLastValidationFilePath()
            Dim currentTime As String = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
            Dim encryptedTime As String = AdvancedEncryption.EncryptWithHardwareKey(currentTime)
            
            File.WriteAllText(validationFilePath, encryptedTime)
        Catch ex As Exception
            LogSecurityEvent($"Failed to update last validation time: {ex.Message}")
        End Try
    End Sub
    
    Private Shared Sub LogSuccessfulValidation()
        Try
            ' تسجيل التحقق الناجح
            Console.WriteLine($"[LICENSE] Successful validation at {DateTime.Now}")
            
            ' إرسال تنبيه (اختياري)
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendToAdmin($"✅ تم التحقق من الترخيص بنجاح - {Environment.UserName}")
                     End Function)
            
        Catch ex As Exception
            ' تجاهل أخطاء التسجيل لعدم تعطيل العملية الأساسية
            Console.WriteLine($"Failed to log successful validation: {ex.Message}")
        End Try
    End Sub
    
    Private Shared Sub LogSecurityEvent(message As String)
        Try
            ' تسجيل الأحداث الأمنية
            Console.WriteLine($"[SECURITY] {DateTime.Now}: {message}")
            
            ' إرسال تنبيه أمني
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendSecurityViolation("License Validation", message)
                     End Function)
            
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub
    
    ' دالة لإنشاء ترخيص جديد (للاستخدام في تطبيق الإدمن)
    Public Shared Function CreateLicense(customerInfo As CustomerInfo, hardwareFingerprint As String) As License
        Try
            Dim license As New License() With {
                .LicenseId = Guid.NewGuid().ToString(),
                .HardwareFingerprint = hardwareFingerprint,
                .CustomerInfo = customerInfo,
                .IssueDate = DateTime.UtcNow,
                .ExpiryDate = DateTime.UtcNow.AddYears(1),
                .IsActive = True,
                .MaxActivations = 1,
                .CurrentActivations = 0
            }
            
            ' إنشاء التوقيع الرقمي
            license.Signature = GenerateLicenseSignature(license)
            
            Return license
        Catch ex As Exception
            Throw New Exception($"فشل في إنشاء الترخيص: {ex.Message}")
        End Try
    End Function
    
    Private Shared Function GenerateLicenseSignature(license As License) As String
        Try
            ' إنشاء التوقيع الرقمي للترخيص
            Dim licenseData As String = $"{license.LicenseId}{license.HardwareFingerprint}{license.IssueDate:yyyyMMdd}"
            Return AdvancedEncryption.ComputeSHA256(licenseData)
        Catch
            Return "SIGNATURE_ERROR"
        End Try
    End Function
    
End Class
