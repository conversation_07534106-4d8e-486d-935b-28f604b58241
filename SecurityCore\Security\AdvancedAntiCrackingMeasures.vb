Imports System.Diagnostics
Imports System.Runtime.InteropServices
Imports System.Threading

Public Class AdvancedAntiCrackingMeasures
    
    ' تقنية Code Cave Detection
    Public Shared Function DetectCodeCaves() As Boolean
        Try
            Dim currentModule As ProcessModule = Process.GetCurrentProcess().MainModule
            Dim imageBase As IntPtr = currentModule.BaseAddress
            Dim imageSize As Integer = currentModule.ModuleMemorySize
            
            ' فحص المناطق غير المستخدمة في الذاكرة
            For offset As Integer = 0 To imageSize - 16 Step 4
                Dim address As IntPtr = IntPtr.Add(imageBase, offset)
                Dim bytes(15) As Byte
                
                Try
                    Marshal.Copy(address, bytes, 0, 16)
                    
                    ' البحث عن أنماط مشبوهة
                    If DetectSuspiciousPattern(bytes) Then
                        Return True
                    End If
                Catch
                    ' تجاهل أخطاء الوصول للذاكرة
                    Continue For
                End Try
            Next
            
            Return False
        Catch
            Return True ' في حالة الخطأ، افترض وجود تلاعب
        End Try
    End Function
    
    Private Shared Function DetectSuspiciousPattern(bytes As Byte()) As Boolean
        ' البحث عن أنماط شائعة في الكراك
        Dim suspiciousPatterns As Byte()() = {
            {&H90, &H90, &H90, &H90}, ' NOP sled
            {&HEB, &HFE}, ' JMP $-2 (infinite loop)
            {&HCC, &HCC, &HCC, &HCC}, ' INT3 breakpoints
            {&H31, &HC0, &H40, &HC3} ' XOR EAX,EAX; INC EAX; RET (common crack pattern)
        }
        
        For Each pattern In suspiciousPatterns
            If ContainsPattern(bytes, pattern) Then
                Return True
            End If
        Next
        
        Return False
    End Function
    
    Private Shared Function ContainsPattern(data As Byte(), pattern As Byte()) As Boolean
        For i As Integer = 0 To data.Length - pattern.Length
            Dim match As Boolean = True
            For j As Integer = 0 To pattern.Length - 1
                If data(i + j) <> pattern(j) Then
                    match = False
                    Exit For
                End If
            Next
            If match Then Return True
        Next
        Return False
    End Function
    
    ' تقنية API Hooking Detection
    Public Shared Function DetectAPIHooks() As Boolean
        Dim criticalAPIs() As String = {
            "kernel32.dll!CreateFileW",
            "kernel32.dll!WriteFile",
            "kernel32.dll!ReadFile",
            "ntdll.dll!NtCreateFile",
            "ntdll.dll!NtWriteFile",
            "user32.dll!SetWindowsHookExW"
        }
        
        For Each api In criticalAPIs
            If IsAPIHooked(api) Then
                Return True
            End If
        Next
        
        Return False
    End Function
    
    Private Shared Function IsAPIHooked(apiName As String) As Boolean
        Try
            Dim parts() As String = apiName.Split("!"c)
            If parts.Length <> 2 Then Return False
            
            Dim moduleName As String = parts(0)
            Dim functionName As String = parts(1)
            
            ' الحصول على عنوان الدالة
            Dim moduleHandle As IntPtr = NativeMethods.GetModuleHandle(moduleName)
            If moduleHandle = IntPtr.Zero Then Return False
            
            Dim procAddress As IntPtr = NativeMethods.GetProcAddress(moduleHandle, functionName)
            If procAddress = IntPtr.Zero Then Return False
            
            ' فحص أول 5 بايت من الدالة للبحث عن JMP hook
            Dim bytes(4) As Byte
            Marshal.Copy(procAddress, bytes, 0, 5)
            
            ' فحص JMP instruction (0xE9)
            If bytes(0) = &HE9 Then
                Return True ' محتمل وجود hook
            End If
            
            ' فحص MOV instruction patterns شائعة في الـ hooks
            If bytes(0) = &H48 AndAlso bytes(1) = &HB8 Then ' MOV RAX, imm64
                Return True
            End If
            
            Return False
        Catch
            Return True ' في حالة الخطأ، افترض وجود hook
        End Try
    End Function
    
    ' تقنية Hardware Breakpoint Detection
    Public Shared Function DetectHardwareBreakpoints() As Boolean
        Try
            ' فحص DR0-DR3 registers
            Dim context As New CONTEXT()
            context.ContextFlags = CONTEXT_DEBUG_REGISTERS
            
            Dim currentThread As IntPtr = NativeMethods.GetCurrentThread()
            If NativeMethods.GetThreadContext(currentThread, context) Then
                Return context.Dr0 <> 0 OrElse context.Dr1 <> 0 OrElse 
                       context.Dr2 <> 0 OrElse context.Dr3 <> 0
            End If
            
            Return False
        Catch
            Return True
        End Try
    End Function
    
    ' تقنية Memory Scanning Protection
    Public Shared Sub ProtectCriticalMemory()
        Try
            ' تغيير أذونات الذاكرة للمناطق الحساسة
            Dim criticalSections As List(Of IntPtr) = GetCriticalMemorySections()
            
            For Each section In criticalSections
                Dim oldProtect As UInteger
                ' جعل المنطقة قابلة للتنفيذ والقراءة فقط
                NativeMethods.VirtualProtect(section, 4096, PAGE_EXECUTE_READ, oldProtect)
            Next
        Catch ex As Exception
            ' تسجيل الخطأ دون إيقاف التطبيق
            LogError($"Failed to protect critical memory: {ex.Message}")
        End Try
    End Sub
    
    Private Shared Function GetCriticalMemorySections() As List(Of IntPtr)
        Dim sections As New List(Of IntPtr)
        
        Try
            ' الحصول على معلومات الوحدة الحالية
            Dim currentModule As ProcessModule = Process.GetCurrentProcess().MainModule
            Dim baseAddress As IntPtr = currentModule.BaseAddress
            
            ' إضافة أقسام مهمة (هذا مثال، يجب تخصيصه حسب التطبيق)
            sections.Add(baseAddress) ' القسم الرئيسي
            sections.Add(IntPtr.Add(baseAddress, &H1000)) ' قسم الكود عادة
            
        Catch ex As Exception
            LogError($"Failed to get critical memory sections: {ex.Message}")
        End Try
        
        Return sections
    End Function
    
    ' تقنية Process Hollowing Detection
    Public Shared Function DetectProcessHollowing() As Boolean
        Try
            Dim currentProcess As Process = Process.GetCurrentProcess()
            Dim expectedPath As String = currentProcess.MainModule.FileName
            Dim actualPath As String = GetActualProcessPath()
            
            ' مقارنة المسار المتوقع مع الفعلي
            If Not expectedPath.Equals(actualPath, StringComparison.OrdinalIgnoreCase) Then
                Return True
            End If
            
            ' فحص تكامل PE header
            Return Not VerifyPEHeaderIntegrity()
            
        Catch
            Return True
        End Try
    End Function
    
    Private Shared Function GetActualProcessPath() As String
        Try
            ' استخدام QueryFullProcessImageName للحصول على المسار الفعلي
            Dim buffer As New Text.StringBuilder(260)
            Dim size As UInteger = CUInt(buffer.Capacity)
            
            If NativeMethods.QueryFullProcessImageName(Process.GetCurrentProcess().Handle, 0, buffer, size) Then
                Return buffer.ToString()
            End If
        Catch
        End Try
        
        Return String.Empty
    End Function
    
    Private Shared Function VerifyPEHeaderIntegrity() As Boolean
        Try
            Dim currentModule As ProcessModule = Process.GetCurrentProcess().MainModule
            Dim baseAddress As IntPtr = currentModule.BaseAddress
            
            ' قراءة DOS header
            Dim dosHeader(63) As Byte
            Marshal.Copy(baseAddress, dosHeader, 0, 64)
            
            ' فحص DOS signature (MZ)
            If dosHeader(0) <> &H4D OrElse dosHeader(1) <> &H5A Then
                Return False
            End If
            
            ' الحصول على عنوان PE header
            Dim peOffset As Integer = BitConverter.ToInt32(dosHeader, 60)
            Dim peAddress As IntPtr = IntPtr.Add(baseAddress, peOffset)
            
            ' قراءة PE signature
            Dim peSignature(3) As Byte
            Marshal.Copy(peAddress, peSignature, 0, 4)
            
            ' فحص PE signature (PE\0\0)
            Return peSignature(0) = &H50 AndAlso peSignature(1) = &H45 AndAlso 
                   peSignature(2) = 0 AndAlso peSignature(3) = 0
            
        Catch
            Return False
        End Try
    End Function
    
    Private Shared Sub LogError(message As String)
        Try
            ' تسجيل الأخطاء (يمكن تحسينه لاحقاً)
            Console.WriteLine($"[ERROR] {DateTime.Now}: {message}")
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub
    
    ' Constants for memory protection
    Private Const PAGE_EXECUTE_READ As UInteger = &H20
    Private Const CONTEXT_DEBUG_REGISTERS As UInteger = &H10
    
End Class
