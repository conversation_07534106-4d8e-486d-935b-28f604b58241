# نظام إدارة التراخيص المحمي - Protected License Management System

## نظرة عامة
نظام شامل لإدارة التراخيص مع حماية متقدمة ضد الكراك والتلاعب، مطور بـ VB.NET مع تكامل Firebase وتنبيهات Telegram.

## مكونات النظام

### 1. SecurityCore (مكتبة الأمان الأساسية)
- **AdvancedEncryption**: تشفير AES و RSA مع مفاتيح مشتقة من العتاد
- **AdvancedAntiCrackingMeasures**: حماية ضد Code Caves، API Hooks، Hardware Breakpoints
- **ProductionLicenseValidator**: التحقق المتقدم من التراخيص
- **AdvancedAlertSystem**: نظام تنبيهات متعدد القنوات
- **SecureTelegramNotifier**: إرسال تنبيهات آمنة عبر Telegram
- **SecureFirebaseClient**: اتصال آمن مع Firebase

### 2. AdminMain (تطبيق الإدارة)
- **AdminMainform**: الواجهة الرئيسية للإدارة
- **CreateLicenseForm**: إنشاء تراخيص جديدة
- **ManageLicensesForm**: إدارة التراخيص الموجودة
- **SecurityReportsForm**: تقارير الأمان والانتهاكات
- **SystemSettingsForm**: إعدادات النظام

### 3. ClientApp (تطبيق العميل)
- **Form1**: نافذة تسجيل الدخول مع التحقق الأمني
- **ClientApp**: التطبيق الرئيسي مع مراقبة أمنية مستمرة
- **ClientSettingsForm**: إعدادات العميل

## الميزات الأمنية

### حماية متقدمة ضد الكراك
- **Code Cave Detection**: اكتشاف تلاعب في الذاكرة
- **API Hooking Detection**: اكتشاف hooks في النظام
- **Hardware Breakpoint Detection**: اكتشاف نقاط التوقف العتادية
- **Process Hollowing Detection**: اكتشاف حقن العمليات

### تشفير متقدم
- **AES-256**: تشفير البيانات الحساسة
- **RSA**: تشفير المفاتيح والتوقيعات
- **Hardware-based Keys**: مفاتيح مشتقة من معلومات العتاد
- **SHA-256**: حساب البصمات والتحقق من التكامل

### نظام التنبيهات
- **Telegram Integration**: تنبيهات فورية عبر Telegram
- **Firebase Logging**: تسجيل الأحداث في السحابة
- **Local Encrypted Logs**: سجلات محلية مشفرة
- **Email Alerts**: تنبيهات عبر البريد الإلكتروني

## متطلبات النظام
- Windows 10/11
- .NET Framework 4.7.2 أو أحدث
- Visual Studio 2019/2022 للتطوير

## التثبيت والإعداد

### 1. بناء المشروع
```bash
# بناء مكتبة SecurityCore
msbuild SecurityCore\SecurityCore.vbproj

# بناء تطبيق الإدارة
msbuild AdminMain\AdminMain\AdminMain.vbproj

# بناء تطبيق العميل
msbuild ClientApp\ClientApp\ClientApp.vbproj
```

### 2. إعداد Firebase
1. إنشاء مشروع Firebase جديد
2. الحصول على مفاتيح API
3. تحديث إعدادات Firebase في `SecureFirebaseClient`

### 3. إعداد Telegram Bot
1. إنشاء بوت جديد عبر @BotFather
2. الحصول على Bot Token
3. تحديث إعدادات Telegram في `SecureTelegramNotifier`

## الاستخدام

### تطبيق الإدارة (AdminMain)
1. تشغيل AdminMain.exe
2. إنشاء تراخيص جديدة
3. إدارة التراخيص الموجودة
4. مراقبة التقارير الأمنية

### تطبيق العميل (ClientApp)
1. تشغيل ClientApp.exe
2. التحقق من الترخيص
3. استخدام الميزات المحمية
4. مراقبة أمنية مستمرة

## هيكل المشروع
```
AdminMain/
├── SecurityCore/           # مكتبة الأمان الأساسية
│   ├── Security/          # وحدات الأمان
│   ├── License/           # إدارة التراخيص
│   ├── Firebase/          # تكامل Firebase
│   ├── Telegram/          # تنبيهات Telegram
│   ├── Models/            # نماذج البيانات
│   └── Win32/             # استدعاءات Windows API
├── AdminMain/             # تطبيق الإدارة
│   └── AdminMain/
└── ClientApp/             # تطبيق العميل
    └── ClientApp/
```

## الأمان والحماية

### تشفير البيانات
- جميع البيانات الحساسة مشفرة بـ AES-256
- المفاتيح مشتقة من معلومات العتاد الفريدة
- التوقيعات الرقمية للتحقق من التكامل

### مراقبة الأمان
- فحص مستمر للتهديدات الأمنية
- تنبيهات فورية عند اكتشاف تلاعب
- إغلاق تلقائي عند اكتشاف تهديد

### حماية ضد التحليل
- تشويش الكود (Code Obfuscation)
- حماية ضد مصححات الأخطاء
- اكتشاف أدوات التحليل والكراك

## المساهمة
هذا مشروع مغلق المصدر للاستخدام التجاري. للاستفسارات حول الترخيص، يرجى التواصل مع المطور.

## الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- إنشاء تذكرة دعم في النظام
- التواصل عبر Telegram المدمج
- مراجعة سجلات الأخطاء المحلية

## إخلاء المسؤولية
هذا النظام مصمم للاستخدام القانوني فقط. المطور غير مسؤول عن أي استخدام غير قانوني أو ضار للنظام.

---
© 2025 - جميع الحقوق محفوظة
