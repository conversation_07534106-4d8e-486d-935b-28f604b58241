Public Class LoginLog
    Public Property Id As String
    Public Property LicenseId As String
    Public Property CustomerId As String
    Public Property Timestamp As DateTime
    Public Property Success As Boolean
    Public Property IPAddress As String
    Public Property PublicIP As String
    Public Property DeviceInfo As String
    Public Property Location As String
    Public Property UserAgent As String
    Public Property SessionId As String
    Public Property Duration As TimeSpan?
    Public Property ErrorMessage As String
    Public Property SecurityFlags As List(Of String)
    Public Property AdditionalData As Dictionary(Of String, Object)
    
    Public Sub New()
        Id = Guid.NewGuid().ToString()
        Timestamp = DateTime.UtcNow
        SecurityFlags = New List(Of String)
        AdditionalData = New Dictionary(Of String, Object)
        SessionId = Guid.NewGuid().ToString()
    End Sub
    
    Public Sub New(licenseId As String, success As Boolean)
        Me.New()
        Me.LicenseId = licenseId
        Me.Success = success
    End Sub
    
    Public Sub AddSecurityFlag(flag As String)
        If Not SecurityFlags.Contains(flag) Then
            SecurityFlags.Add(flag)
        End If
    End Sub
    
    Public Function HasSecurityFlag(flag As String) As Boolean
        Return SecurityFlags.Contains(flag)
    End Function
    
    Public Function ToJson() As String
        Return Newtonsoft.Json.JsonConvert.SerializeObject(Me, Newtonsoft.Json.Formatting.Indented)
    End Function
    
    Public Shared Function FromJson(json As String) As LoginLog
        Return Newtonsoft.Json.JsonConvert.DeserializeObject(Of LoginLog)(json)
    End Function
    
    Public Overrides Function ToString() As String
        Dim status As String = If(Success, "نجح", "فشل")
        Return $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] {status} - {IPAddress} - {DeviceInfo}"
    End Function
End Class
