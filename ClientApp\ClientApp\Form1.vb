﻿Imports SecurityCore
Imports SecurityCore.Security
Imports SecurityCore.Models
Imports SecurityCore.Telegram
Imports System.Threading.Tasks

Public Class Form1

    Private _isSecurityInitialized As Boolean = False
    Private _licenseValidator As ProductionLicenseValidator

    Private Sub SetupFontsAndRendering()
        Try
            ' إعداد الخطوط الافتراضية
            Me.Font = New Font("Tahom<PERSON>", 9, FontStyle.Regular)

            ' إعداد إعدادات الرسم
            Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or
                       ControlStyles.UserPaint Or
                       ControlStyles.DoubleBuffer Or
                       ControlStyles.ResizeRedraw, True)

            ' إعداد خصائص النموذج
            Me.AutoScaleMode = AutoScaleMode.Font
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True

            ' تطبيق الخط على جميع العناصر
            ApplyFontToAllControls(<PERSON>, <PERSON><PERSON>Font)

        Catch ex As Exception
            ' تجاهل أخطاء إعداد الخطوط
            Console.WriteLine($"تحذير: فشل في إعداد الخطوط: {ex.Message}")
        End Try
    End Sub

    Private Sub ApplyFontToAllControls(parent As Control, font As Font)
        Try
            For Each ctrl As Control In parent.Controls
                If TypeOf ctrl Is GroupBox OrElse
                   TypeOf ctrl Is Label OrElse
                   TypeOf ctrl Is Button OrElse
                   TypeOf ctrl Is TextBox Then
                    ctrl.Font = font
                End If

                ' تطبيق على العناصر الفرعية
                If ctrl.HasChildren Then
                    ApplyFontToAllControls(ctrl, font)
                End If
            Next
        Catch ex As Exception
            ' تجاهل أخطاء تطبيق الخطوط
            Console.WriteLine($"تحذير: فشل في تطبيق الخط: {ex.Message}")
        End Try
    End Sub

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            ' إعداد الخطوط والرسم أولاً
            SetupFontsAndRendering()

            ' تهيئة أنظمة الأمان قبل أي شيء آخر
            InitializeSecuritySystems()

            ' تحديث واجهة تسجيل الدخول
            InitializeLoginUI()

        Catch ex As Exception
            ' في حالة فشل التهيئة، إغلاق التطبيق
            MessageBox.Show($"فشل في تهيئة النظام: {ex.Message}", "خطأ حرج", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Application.Exit()
        End Try
    End Sub

    Private Sub InitializeSecuritySystems()
        Try
            ' تهيئة الحماية المتقدمة
            AdvancedSecurityManager.InitializeProductionSecurity()

            ' تهيئة مدقق التراخيص
            _licenseValidator = New ProductionLicenseValidator()

            _isSecurityInitialized = True

        Catch ex As Exception
            Throw New SecurityException($"فشل في تهيئة أنظمة الأمان: {ex.Message}")
        End Try
    End Sub

    Private Sub InitializeLoginUI()
        Try
            ' تحديث عنوان النافذة
            Me.Text = "تسجيل الدخول الآمن"

            ' تحديث نص الزر
            btnLogin.Text = "دخول"
            btnLogin.BackColor = Color.Green
            btnLogin.ForeColor = Color.White
            btnLogin.Font = New Font("Tahoma", 12, FontStyle.Bold)

            ' إضافة معلومات الحالة
            Me.BackColor = Color.FromArgb(240, 240, 240)

        Catch ex As Exception
            Throw New Exception($"فشل في تهيئة واجهة تسجيل الدخول: {ex.Message}")
        End Try
    End Sub

    Private Async Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        Try
            ' تعطيل الزر أثناء المعالجة
            btnLogin.Enabled = False
            btnLogin.Text = "جاري التحقق..."

            ' تنفيذ عملية تسجيل الدخول الآمنة
            Dim loginResult As Boolean = Await PerformSecureLogin()

            If loginResult Then
                ' تسجيل دخول ناجح - فتح التطبيق الرئيسي
                OpenMainApplication()
            Else
                ' فشل تسجيل الدخول
                ShowLoginFailure()
            End If

        Catch ex As Exception
            ' معالجة الأخطاء
            HandleLoginError(ex)
        Finally
            ' إعادة تفعيل الزر
            btnLogin.Enabled = True
            btnLogin.Text = "دخول"
        End Try
    End Sub

    Private Async Function PerformSecureLogin() As Task(Of Boolean)
        Try
            ' الخطوة 1: فحص أنظمة الأمان
            If Not PerformSecurityChecks() Then
                Await SendSecurityAlert("فشل في فحوصات الأمان الأولية")
                Return False
            End If

            ' الخطوة 2: التحقق من الترخيص
            Dim licenseValidation As ValidationResult = Await ValidateLicense()
            If Not licenseValidation.IsValid Then
                Await SendSecurityAlert($"فشل التحقق من الترخيص: {licenseValidation.ErrorMessage}")
                Return False
            End If

            ' الخطوة 3: تسجيل محاولة الدخول
            Await LogLoginAttempt(True)

            ' الخطوة 4: إرسال تنبيه نجاح تسجيل الدخول
            Await SendLoginSuccessAlert()

            Return True

        Catch ex As Exception
            ' تسجيل محاولة دخول فاشلة
            Task.Run(Async Function()
                         Await LogLoginAttempt(False, ex.Message)
                     End Function)
            Throw
        End Try
    End Function

    Private Function PerformSecurityChecks() As Boolean
        Try
            ' فحص Code Caves
            If AdvancedAntiCrackingMeasures.DetectCodeCaves() Then
                LogSecurityViolation("Code Cave Detection", "تم اكتشاف تلاعب في الذاكرة")
                Return False
            End If

            ' فحص API Hooks
            If AdvancedAntiCrackingMeasures.DetectAPIHooks() Then
                LogSecurityViolation("API Hook Detection", "تم اكتشاف hooks في النظام")
                Return False
            End If

            ' فحص Hardware Breakpoints
            If AdvancedAntiCrackingMeasures.DetectHardwareBreakpoints() Then
                LogSecurityViolation("Hardware Breakpoint Detection", "تم اكتشاف نقاط توقف عتادية")
                Return False
            End If

            ' فحص Process Hollowing
            If AdvancedAntiCrackingMeasures.DetectProcessHollowing() Then
                LogSecurityViolation("Process Hollowing Detection", "تم اكتشاف حقن عملية")
                Return False
            End If

            Return True

        Catch ex As Exception
            LogSecurityViolation("Security Check Error", $"خطأ في فحوصات الأمان: {ex.Message}")
            Return False
        End Try
    End Function

    Private Async Function ValidateLicense() As Task(Of ValidationResult)
        Try
            ' التحقق من الترخيص باستخدام النظام المتقدم
            Return Await Task.Run(Function() ProductionLicenseValidator.ValidateProductionLicense())

        Catch ex As Exception
            Return New ValidationResult(False, $"خطأ في التحقق من الترخيص: {ex.Message}")
        End Try
    End Function

    Private Async Function LogLoginAttempt(success As Boolean, Optional errorMessage As String = "") As Task
        Try
            Dim loginLog As New LoginLog() With {
                .Success = success,
                .IPAddress = Await GetLocalIPAddress(),
                .DeviceInfo = GetDeviceInfo(),
                .ErrorMessage = errorMessage
            }

            ' إضافة معلومات إضافية
            loginLog.AdditionalData("HardwareFingerprint") = GetHardwareFingerprint()
            loginLog.AdditionalData("ApplicationVersion") = GetApplicationVersion()
            loginLog.AdditionalData("OSVersion") = Environment.OSVersion.ToString()

            ' تسجيل في النظام المحلي
            LogLoginLocally(loginLog)

            ' إرسال للخادم (إذا كان متاح)
            Await SendLoginLogToServer(loginLog)

        Catch ex As Exception
            ' تجاهل أخطاء التسجيل لعدم تعطيل العملية الأساسية
            Console.WriteLine($"فشل في تسجيل محاولة الدخول: {ex.Message}")
        End Try
    End Function

    Private Sub LogLoginLocally(loginLog As LoginLog)
        Try
            Dim logDir As String = IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "ClientApp", "Logs")
            If Not IO.Directory.Exists(logDir) Then
                IO.Directory.CreateDirectory(logDir)
            End If

            Dim logFile As String = IO.Path.Combine(logDir, $"login_{DateTime.Now:yyyyMM}.log")
            Dim logEntry As String = loginLog.ToJson()

            ' تشفير السجل
            Dim encryptedEntry As String = AdvancedEncryption.EncryptWithHardwareKey(logEntry)

            IO.File.AppendAllText(logFile, encryptedEntry & Environment.NewLine)

        Catch ex As Exception
            Console.WriteLine($"فشل في التسجيل المحلي: {ex.Message}")
        End Try
    End Sub

    Private Async Function SendLoginLogToServer(loginLog As LoginLog) As Task
        Try
            ' إرسال سجل تسجيل الدخول للخادم
            ' هذا مثال - يجب تنفيذه مع Firebase الفعلي
            Await Task.Delay(100) ' محاكاة الإرسال

        Catch ex As Exception
            Console.WriteLine($"فشل في إرسال السجل للخادم: {ex.Message}")
        End Try
    End Function

    Private Async Function SendSecurityAlert(message As String) As Task
        Try
            Dim incident As New SecurityIncident("Security Violation", message, SecuritySeverity.High)
            incident.ActionTaken = "تم منع تسجيل الدخول"

            Await AdvancedAlertSystem.SendCriticalSecurityAlert(incident)

        Catch ex As Exception
            Console.WriteLine($"فشل في إرسال تنبيه الأمان: {ex.Message}")
        End Try
    End Function

    Private Async Function SendLoginSuccessAlert() As Task
        Try
            Dim deviceInfo As String = GetDeviceInfo()
            Dim location As String = Await GetApproximateLocation()

            Await SecureTelegramNotifier.SendLoginAlert(
                Environment.UserName,
                deviceInfo,
                location
            )

        Catch ex As Exception
            Console.WriteLine($"فشل في إرسال تنبيه نجاح تسجيل الدخول: {ex.Message}")
        End Try
    End Function

    Private Sub OpenMainApplication()
        Try
            ' إخفاء نافذة تسجيل الدخول
            Me.Hide()

            ' فتح التطبيق الرئيسي
            Dim mainApp As New ClientApp()
            mainApp.ShowDialog()

            ' إغلاق التطبيق بعد إغلاق النافذة الرئيسية
            Application.Exit()

        Catch ex As Exception
            MessageBox.Show($"خطأ في فتح التطبيق الرئيسي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowLoginFailure()
        MessageBox.Show("فشل في تسجيل الدخول. يرجى التحقق من صحة الترخيص.", "فشل تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    End Sub

    Private Sub HandleLoginError(ex As Exception)
        ' تسجيل الخطأ
        Console.WriteLine($"خطأ في تسجيل الدخول: {ex.Message}")

        ' إرسال تنبيه أمني
        Task.Run(Async Function()
                     Await SendSecurityAlert($"خطأ في عملية تسجيل الدخول: {ex.Message}")
                 End Function)

        ' عرض رسالة للمستخدم
        MessageBox.Show("حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Sub

    Private Sub LogSecurityViolation(violationType As String, details As String)
        Try
            ' تسجيل الانتهاك الأمني
            Console.WriteLine($"[SECURITY VIOLATION] {violationType}: {details}")

            ' إرسال تنبيه فوري
            Task.Run(Async Function()
                         Await SecureTelegramNotifier.SendSecurityViolation(violationType, details)
                     End Function)

        Catch ex As Exception
            Console.WriteLine($"فشل في تسجيل الانتهاك الأمني: {ex.Message}")
        End Try
    End Sub

    ' دوال مساعدة
    Private Async Function GetLocalIPAddress() As Task(Of String)
        Try
            Using client As New Net.Http.HttpClient()
                client.Timeout = TimeSpan.FromSeconds(5)
                Return Await client.GetStringAsync("https://ipv4.icanhazip.com/")
            End Using
        Catch
            Return "Unknown"
        End Try
    End Function

    Private Function GetDeviceInfo() As String
        Try
            Return $"{Environment.MachineName} ({Environment.OSVersion})"
        Catch
            Return "Unknown Device"
        End Try
    End Function

    Private Function GetHardwareFingerprint() As String
        Try
            Return AdvancedEncryption.ComputeSHA256("HardwareFingerprint")
        Catch
            Return "Unknown"
        End Try
    End Function

    Private Function GetApplicationVersion() As String
        Try
            Return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
        Catch
            Return "Unknown"
        End Try
    End Function

    Private Async Function GetApproximateLocation() As Task(Of String)
        Try
            ' الحصول على الموقع التقريبي
            Return "Unknown Location"
        Catch
            Return "Unknown"
        End Try
    End Function

    Private Sub Form1_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            ' تنظيف الموارد عند الإغلاق
            If _isSecurityInitialized Then
                ' إرسال تنبيه إغلاق التطبيق
                Task.Run(Async Function()
                             Await SecureTelegramNotifier.SendToAdmin("🔴 تم إغلاق تطبيق العميل")
                         End Function)
            End If

        Catch ex As Exception
            Console.WriteLine($"خطأ في إغلاق التطبيق: {ex.Message}")
        End Try
    End Sub

    Private Sub btnExit_Click(sender As Object, e As EventArgs) Handles btnExit.Click
        Application.Exit()
    End Sub

    ' معالجة أخطاء الرسم
    Protected Overrides Sub OnPaint(e As PaintEventArgs)
        Try
            MyBase.OnPaint(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم المتعلقة بالخطوط
            Console.WriteLine($"تحذير: خطأ في الرسم: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في الرسم: {ex.Message}")
        End Try
    End Sub

    Protected Overrides Sub OnPaintBackground(e As PaintEventArgs)
        Try
            MyBase.OnPaintBackground(e)
        Catch ex As ArgumentException
            ' تجاهل أخطاء الرسم
            Console.WriteLine($"تحذير: خطأ في رسم الخلفية: {ex.Message}")
        Catch ex As Exception
            Console.WriteLine($"خطأ غير متوقع في رسم الخلفية: {ex.Message}")
        End Try
    End Sub

End Class
