# دليل الإعداد والتشغيل - Setup Guide

## خطوات الإعداد الأولي

### 1. متطلبات النظام
- Windows 10/11 (64-bit)
- .NET Framework 4.7.2 أو أحدث
- Visual Studio 2019/2022 (للتطوير)
- اتصال إنترنت (للتنبيهات والتحديثات)

### 2. بناء المشروع

#### أ. بناء مكتبة SecurityCore
```bash
cd "C:\Path\To\Project"
& "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" SecurityCore\SecurityCore.vbproj
```

#### ب. بناء تطبيق الإدارة
```bash
& "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" AdminMain\AdminMain\AdminMain.vbproj
```

#### ج. بناء تطبيق العميل
```bash
& "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" ClientApp\ClientApp\ClientApp.vbproj
```

### 3. إعداد Firebase

#### أ. إنشاء مشروع Firebase
1. الذهاب إلى [Firebase Console](https://console.firebase.google.com/)
2. إنشاء مشروع جديد
3. تفعيل Realtime Database
4. إعداد قواعد الأمان

#### ب. الحصول على مفاتيح API
1. الذهاب إلى Project Settings
2. نسخ Web API Key
3. نسخ Database URL

#### ج. تحديث الإعدادات
```vb
' في SecureFirebaseClient.vb
Private Shared ReadOnly _encryptedConfig As String = "YOUR_ENCRYPTED_CONFIG"
```

### 4. إعداد Telegram Bot

#### أ. إنشاء البوت
1. التحدث مع @BotFather في Telegram
2. استخدام الأمر `/newbot`
3. اختيار اسم للبوت
4. الحصول على Bot Token

#### ب. إعداد القنوات
1. إنشاء قناة للتنبيهات
2. إضافة البوت كمدير
3. الحصول على Chat ID

#### ج. تحديث الإعدادات
```vb
' في SecureTelegramNotifier.vb
Private Shared ReadOnly _encryptedBotToken As String = "YOUR_ENCRYPTED_TOKEN"
Private Static ReadOnly _encryptedAdminChatId As String = "YOUR_ENCRYPTED_CHAT_ID"
```

### 5. تشفير الإعدادات

#### أ. تشغيل دالة التشفير
```vb
' استخدام هذه الدالة مرة واحدة لتشفير البيانات
SecureTelegramNotifier.SetupEncryptedCredentials(
    "YOUR_BOT_TOKEN",
    "YOUR_ADMIN_CHAT_ID", 
    "YOUR_CHANNEL_ID"
)
```

#### ب. حفظ القيم المشفرة
1. تشغيل الدالة أعلاه
2. نسخ القيم المشفرة من Console
3. تحديث الثوابت في الكود

## التشغيل والاختبار

### 1. اختبار مكتبة SecurityCore
```vb
' اختبار التشفير
Dim testData As String = "Test Data"
Dim encrypted As String = AdvancedEncryption.EncryptWithHardwareKey(testData)
Dim decrypted As String = AdvancedEncryption.DecryptWithHardwareKey(encrypted)

' اختبار الحماية ضد الكراك
Dim hasCodeCaves As Boolean = AdvancedAntiCrackingMeasures.DetectCodeCaves()
Dim hasAPIHooks As Boolean = AdvancedAntiCrackingMeasures.DetectAPIHooks()
```

### 2. اختبار تطبيق الإدارة
1. تشغيل AdminMain.exe
2. التحقق من تحميل الواجهة
3. اختبار إنشاء ترخيص جديد
4. التحقق من التنبيهات

### 3. اختبار تطبيق العميل
1. تشغيل ClientApp.exe
2. اختبار تسجيل الدخول
3. التحقق من المراقبة الأمنية
4. اختبار الميزات المحمية

## استكشاف الأخطاء

### مشاكل البناء الشائعة

#### خطأ: "SecurityException is not defined"
**الحل**: التأكد من إضافة SecurityException.vb إلى المشروع

#### خطأ: "Timer does not contain Interval"
**الحل**: استخدام System.Windows.Forms.Timer بدلاً من System.Threading.Timer

#### خطأ: "Await cannot be used in Catch"
**الحل**: استخدام Task.Run داخل Catch blocks

### مشاكل التشغيل الشائعة

#### خطأ: "Bot token not configured"
**الحل**: 
1. التأكد من تشفير Bot Token
2. تحديث _encryptedBotToken في الكود

#### خطأ: "Firebase config not found"
**الحل**:
1. التأكد من إعداد Firebase بشكل صحيح
2. تحديث _encryptedConfig في SecureFirebaseClient

#### خطأ: "Hardware fingerprint failed"
**الحل**:
1. التحقق من أذونات النظام
2. تشغيل التطبيق كمدير إذا لزم الأمر

## الأمان والحماية

### إعدادات الأمان الموصى بها
1. تشغيل التطبيق في بيئة محمية
2. استخدام مضاد فيروسات محدث
3. تفعيل Windows Defender
4. تحديث النظام بانتظام

### مراقبة الأمان
1. مراجعة سجلات الأمان يومياً
2. التحقق من تنبيهات Telegram
3. مراقبة استخدام الذاكرة والمعالج
4. فحص ملفات النظام بانتظام

### النسخ الاحتياطي
1. نسخ احتياطي من قاعدة البيانات أسبوعياً
2. حفظ إعدادات التشفير بأمان
3. نسخ احتياطي من سجلات الأمان
4. اختبار استعادة البيانات شهرياً

## الصيانة الدورية

### يومياً
- مراجعة سجلات الأمان
- التحقق من تنبيهات النظام
- مراقبة أداء التطبيق

### أسبوعياً
- تحديث قواعد الأمان
- مراجعة التراخيص المنتهية
- فحص تكامل الملفات

### شهرياً
- تحديث مفاتيح التشفير
- مراجعة إعدادات Firebase
- اختبار نظام الاستعادة

## الدعم الفني

### معلومات الاتصال
- Telegram: @YourSupportBot
- Email: <EMAIL>
- Phone: +1-XXX-XXX-XXXX

### معلومات مطلوبة للدعم
1. رقم إصدار التطبيق
2. نظام التشغيل والإصدار
3. وصف تفصيلي للمشكلة
4. سجلات الأخطاء (إن وجدت)
5. خطوات إعادة إنتاج المشكلة

---
© 2025 - دليل الإعداد والتشغيل
