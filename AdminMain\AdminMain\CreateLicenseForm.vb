Imports SecurityCore
Imports SecurityCore.Models
Imports SecurityCore.Security

Public Class CreateLicenseForm
    Inherits Form

    Private Sub CreateLicenseForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            InitializeForm()
        Catch ex As Exception
            MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub InitializeForm()
        Me.Text = "إنشاء ترخيص جديد"
        Me.Size = New Size(500, 400)
        Me.StartPosition = FormStartPosition.CenterParent

        ' يمكن إضافة عناصر التحكم هنا لاحقاً
    End Sub

    Private Sub btnCreate_Click(sender As Object, e As EventArgs) ' Handles btnCreate.Click
        Try
            ' إنشاء ترخيص جديد
            Dim customerInfo As New CustomerInfo() With {
                .Name = "عميل تجريبي",
                .Email = "<EMAIL>"
            }

            Dim hardwareFingerprint As String = "TEST_HARDWARE_FINGERPRINT"

            Dim license As License = ProductionLicenseValidator.CreateLicense(customerInfo, hardwareFingerprint)

            MessageBox.Show($"تم إنشاء الترخيص بنجاح!{Environment.NewLine}معرف الترخيص: {license.LicenseId}",
                          "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Me.DialogResult = DialogResult.OK
            Me.Close()

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء الترخيص: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) ' Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

End Class
