Public Class LicenseValidator
    
    Public Shared Function ValidateLicense(license As License) As ValidationResult
        Try
            ' التحقق من صحة الترخيص
            If license Is Nothing Then
                Return New ValidationResult(False, "الترخيص غير موجود")
            End If
            
            Return New ValidationResult(True, "الترخيص صحيح")
        Catch ex As Exception
            Return New ValidationResult(False, $"خطأ في التحقق: {ex.Message}")
        End Try
    End Function
    
End Class
