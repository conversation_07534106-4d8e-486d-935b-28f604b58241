Public Class SecurityIncident
    Public Property Id As String
    Public Property Timestamp As DateTime
    Public Property Type As String
    Public Property Description As String
    Public Property Severity As SecuritySeverity
    Public Property MachineName As String
    Public Property UserName As String
    Public Property ProcessName As String
    Public Property HardwareFingerprint As String
    Public Property LocalIP As String
    Public Property PublicIP As String
    Public Property Location As String
    Public Property ActionTaken As String
    Public Property AdditionalData As Dictionary(Of String, Object)
    
    Public Sub New()
        Id = Guid.NewGuid().ToString()
        Timestamp = DateTime.UtcNow
        MachineName = Environment.MachineName
        UserName = Environment.UserName
        ProcessName = Process.GetCurrentProcess().ProcessName
        AdditionalData = New Dictionary(Of String, Object)
    End Sub
    
    Public Sub New(incidentType As String, description As String, severity As SecuritySeverity)
        Me.New()
        Me.Type = incidentType
        Me.Description = description
        Me.Severity = severity
    End Sub
End Class

Public Enum SecuritySeverity
    Low = 1
    Medium = 2
    High = 3
    Critical = 4
End Enum
